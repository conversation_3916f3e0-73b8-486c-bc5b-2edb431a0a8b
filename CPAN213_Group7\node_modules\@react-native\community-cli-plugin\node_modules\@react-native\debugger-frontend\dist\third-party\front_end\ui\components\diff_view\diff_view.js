import*as e from"../../../core/i18n/i18n.js";import*as i from"../../../third_party/diff/diff.js";import*as t from"../../lit-html/lit-html.js";import*as n from"../code_highlighter/code_highlighter.js";const r=new CSSStyleSheet;r.replaceSync(".diff-listing{display:grid;grid-template-columns:max-content max-content max-content auto;font-family:var(--source-code-font-family);font-size:var(--source-code-font-size);white-space:pre;line-height:1.2em;user-select:text}.diff-line-number{color:var(--sys-color-token-subtle);padding:0 3px 0 9px;text-align:right;user-select:none}.diff-line-marker{border-right:1px solid var(--sys-color-divider);width:20px;text-align:center}.diff-line-content{padding:0 4px}.diff-line-marker-addition,\n.diff-line-addition{background-color:var(--sys-color-surface-green)}.diff-line-marker-deletion,\n.diff-line-deletion{background-color:var(--sys-color-surface-error)}.diff-line-addition .inner-diff{background-color:color-mix(in sRGB,var(--ref-palette-green70) 40%,transparent)}.diff-line-deletion .inner-diff{background-color:color-mix(in sRGB,var(--ref-palette-error60) 40%,transparent)}.diff-hidden-text{display:inline-block;width:0;overflow:hidden}.diff-line-equal{opacity:50%}.diff-line-spacer{text-align:center;background-color:var(--sys-color-surface5)}\n/*# sourceURL=diffView.css */\n");const o={deletions:"Deletion:",additions:"Addition:",changesDiffViewer:"Changes diff viewer",SkippingDMatchingLines:"( … Skipping {PH1} matching lines … )"},s=e.i18n.registerUIStrings("ui/components/diff_view/DiffView.ts",o),a=e.i18n.getLocalizedString.bind(void 0,s);function l(e){let t=0,n=0;const r=[],s=[],l=[];for(let t=0;t<e.length;++t){const n=e[t];switch(n[0]){case i.Diff.Operation.Equal:l.push(...d(n[1],0===t,t===e.length-1)),r.push(...n[1]),s.push(...n[1]);break;case i.Diff.Operation.Insert:for(const e of n[1])l.push(c(e,"addition"));s.push(...n[1]);break;case i.Diff.Operation.Delete:if(r.push(...n[1]),e[t+1]&&e[t+1][0]===i.Diff.Operation.Insert)t++,l.push(...f(n[1].join("\n"),e[t][1].join("\n"))),s.push(...e[t][1]);else for(const e of n[1])l.push(c(e,"deletion"))}}return{originalLines:r,currentLines:s,rows:l};function d(e,i,r){const s=[];if(!i){for(let i=0;i<3&&i<e.length;i++)s.push(c(e[i],"equal"));e.length>7&&!r&&s.push(c(a(o.SkippingDMatchingLines,{PH1:e.length-6}),"spacer"))}if(!r){const r=Math.max(e.length-3-1,i?0:3);let o=e.length-3-1;i||(o-=3),o>0&&(n+=o,t+=o);for(let i=r;i<e.length;i++)s.push(c(e[i],"equal"))}return s}function f(e,t){const n=i.Diff.DiffWrapper.charDiff(e,t,!0),r=[c("","deletion")],o=[c("","addition")];for(const e of n){const t=e[1],n=e[0],s=n===i.Diff.Operation.Equal?"":"inner-diff",a=t.split("\n");for(let e=0;e<a.length;e++)e>0&&n!==i.Diff.Operation.Insert&&r.push(c("","deletion")),e>0&&n!==i.Diff.Operation.Delete&&o.push(c("","addition")),a[e]&&(n!==i.Diff.Operation.Insert&&r[r.length-1].tokens.push({text:a[e],className:s}),n!==i.Diff.Operation.Delete&&o[o.length-1].tokens.push({text:a[e],className:s}))}return r.concat(o)}function c(e,i){return"addition"===i&&t++,"deletion"===i&&n++,"equal"===i&&(n++,t++),{originalLineNumber:n,currentLineNumber:t,tokens:e?[{text:e,className:"inner-diff"}]:[],type:i}}}function d(e){const i=new Map;for(let t=0,n=0;n<e.length;n++)i.set(n+1,t),t+=e[n].length+1;return i}class f{originalHighlighter;originalMap;currentHighlighter;currentMap;constructor(e,i,t,n){this.originalHighlighter=e,this.originalMap=i,this.currentHighlighter=t,this.currentMap=n}#e(e){return t.html`
      <div class="diff-listing" aria-label=${a(o.changesDiffViewer)}>
        ${e.map((e=>this.#i(e)))}
      </div>`}#i(e){const i="equal"===e.type||"deletion"===e.type?String(e.originalLineNumber):"",n="equal"===e.type||"addition"===e.type?String(e.currentLineNumber):"";let r="",s="diff-line-marker",l=null;return"addition"===e.type?(r="+",s+=" diff-line-addition",l=t.html`<span class="diff-hidden-text">${a(o.additions)}</span>`):"deletion"===e.type&&(r="-",s+=" diff-line-deletion",l=t.html`<span class="diff-hidden-text">${a(o.deletions)}</span>`),t.html`
      <div class="diff-line-number" aria-hidden="true">${i}</div>
      <div class="diff-line-number" aria-hidden="true">${n}</div>
      <div class=${s} aria-hidden="true">${r}</div>
      <div class="diff-line-content diff-line-${e.type}" data-line-number=${n}>${l}${this.#t(e)}</div>`}#t(e){if("spacer"===e.type)return e.tokens.map((e=>t.html`${e.text}`));const[i,n]="deletion"===e.type?[this.originalHighlighter,this.originalMap.get(e.originalLineNumber)]:[this.currentHighlighter,this.currentMap.get(e.currentLineNumber)],r=[];let o=n;for(const n of e.tokens){const e=[];i.highlightRange(o,o+n.text.length,((i,n)=>{e.push(n?t.html`<span class=${n}>${i}</span>`:i)})),r.push(n.className?t.html`<span class=${n.className}>${e}</span>`:t.html`${e}`),o+=n.text.length}return r}static async render(e,i,r){const{originalLines:o,currentLines:s,rows:a}=l(e),c=new f(await n.CodeHighlighter.create(o.join("\n"),i),d(o),await n.CodeHighlighter.create(s.join("\n"),i),d(s));t.render(c.#e(a),r,{host:this})}}class c extends HTMLElement{static litTagName=t.literal`devtools-diff-view`;#n=this.attachShadow({mode:"open"});loaded;constructor(e){super(),this.#n.adoptedStyleSheets=[r,n.Style.default],this.loaded=e?f.render(e.diff,e.mimeType,this.#n):Promise.resolve()}set data(e){this.loaded=f.render(e.diff,e.mimeType,this.#n)}}customElements.define("devtools-diff-view",c);var h=Object.freeze({__proto__:null,buildDiffRows:l,DiffView:c});export{h as DiffView};
