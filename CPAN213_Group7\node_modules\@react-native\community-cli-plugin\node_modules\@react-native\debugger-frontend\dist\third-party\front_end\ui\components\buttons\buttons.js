import*as r from"../../lit-html/lit-html.js";import*as o from"../../visual_logging/visual_logging.js";import*as t from"../helpers/helpers.js";import*as e from"../icon_button/icon_button.js";const s=new CSSStyleSheet;s.replaceSync("*{margin:0;padding:0;box-sizing:border-box}*:focus,\n*:focus-visible,\n:host(:focus),\n:host(:focus-visible){outline:none}:host{display:inline-flex;flex-direction:row}button{--button-has-right-border-radius:calc(1 - var(--override-button-no-right-border-radius, 0));--button-border-size:1px;--button-height:24px;--button-width:100%;align-items:center;background:transparent;border-radius:12px calc(var(--button-has-right-border-radius) * 12px) calc(var(--button-has-right-border-radius) * 12px) 12px;display:inline-flex;font-family:inherit;font-size:12px;font-weight:500;height:var(--button-height);justify-content:center;line-height:14px;padding:0;white-space:nowrap;width:var(--button-width);devtools-icon{width:calc(var(--button-width) - 4px);height:calc(var(--button-height) - 4px)}&.small{--button-height:20px;border-radius:2px calc(var(--button-has-right-border-radius) * 2px) calc(var(--button-has-right-border-radius) * 2px) 2px}&.toolbar,\n  &.round{--button-height:24px;--button-width:24px;border:none;overflow:hidden;&.small{--button-height:20px;--button-width:20px}}&.toolbar{border-radius:2px calc(var(--button-has-right-border-radius) * 2px) calc(var(--button-has-right-border-radius) * 2px) 2px}&.round{border-radius:100%}&.primary{border:var(--button-border-size) solid var(--sys-color-primary);background:var(--sys-color-primary);color:var(--sys-color-on-primary);devtools-icon{color:var(--sys-color-cdt-base-container)}}&.tonal{border:var(--button-border-size) solid transparent;background:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container);devtools-icon{color:var(--sys-color-on-tonal-container)}}&.secondary{border:var(--button-border-size) solid var(--sys-color-tonal-outline);background:var(--sys-color-cdt-base-container);color:var(--sys-color-primary);devtools-icon{color:var(--icon-primary)}}&.primary-toolbar{devtools-icon{color:var(--icon-primary)}}&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;&.toolbar,\n    &.round{background-color:var(--sys-color-tonal-container);outline:none}}&:disabled{devtools-icon{color:var(--icon-disabled)}}&.text-with-icon{padding:0 calc(12px - var(--button-border-size)) 0 calc(8px - var(--button-border-size));devtools-icon{width:16px;height:16px;margin-right:4px}&.small{padding:0 calc(9px - var(--button-border-size)) 0 calc(3px - var(--button-border-size))}}&.only-text{padding:5px 12px}&:hover{&.primary{background:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%);border:var(--button-border-size) solid color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%)}&.tonal{background:color-mix(in sRGB,var(--sys-color-tonal-container),var(--sys-color-state-hover-on-subtle))}&.secondary{background:var(--sys-color-state-hover-on-subtle)}&.toolbar,\n    &.round{background-color:var(--sys-color-state-hover-on-subtle)}&.toobar{devtools-icon{color:var(--icon-default-hover)}}}&:active,\n  &.active{&.primary{background:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%);border:var(--button-border-size) solid color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%)}&.tonal{background:color-mix(in sRGB,var(--sys-color-tonal-container),var(--sys-color-state-ripple-primary))}&.secondary{background-color:var(--sys-color-surface-variant)}&.toolbar,\n    &.round{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&.toolbar{devtools-icon{color:var(--icon-toggled)}}}&:disabled,\n  &:disabled:hover{&.primary{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}&.tonal{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);background:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}&.secondary{border:var(--button-border-size) solid var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}&.toolbar,\n    &.round{background:var(--sys-color-cdt-base-container);color:var(--sys-color-state-disabled)}}}.spinner{display:block;width:12px;height:12px;border-radius:6px;border:2px solid var(--sys-color-cdt-base-container);animation:spinner-animation 1s linear infinite;border-right-color:transparent;margin-right:4px;&.secondary{border:2px solid var(--sys-color-primary);border-right-color:transparent}&.disabled{border:2px solid var(--sys-color-state-disabled);border-right-color:transparent}}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}\n/*# sourceURL=button.css */\n");class i extends HTMLElement{static formAssociated=!0;static litTagName=r.literal`devtools-button`;#r=this.attachShadow({mode:"open",delegatesFocus:!0});#o=this.#t.bind(this);#e=this.#s.bind(this);#i={size:"MEDIUM",disabled:!1,active:!1,spinner:!1,type:"button"};#n=!0;#a=this.attachInternals();constructor(){super(),this.setAttribute("role","presentation"),this.addEventListener("click",this.#e,!0)}set data(r){this.#i.variant=r.variant,this.#i.iconUrl=r.iconUrl,this.#i.iconName=r.iconName,this.#i.size="MEDIUM","size"in r&&r.size&&(this.#i.size=r.size),this.#i.active=Boolean(r.active),this.#i.spinner=Boolean("spinner"in r&&r.spinner),this.#i.type="button","type"in r&&r.type&&(this.#i.type=r.type),this.#l(r.disabled||!1),this.#i.title=r.title,this.#i.jslogContext=r.jslogContext,t.ScheduledRender.scheduleRender(this,this.#o)}set iconUrl(r){this.#i.iconUrl=r,t.ScheduledRender.scheduleRender(this,this.#o)}set iconName(r){this.#i.iconName=r,t.ScheduledRender.scheduleRender(this,this.#o)}set variant(r){this.#i.variant=r,t.ScheduledRender.scheduleRender(this,this.#o)}set size(r){this.#i.size=r,t.ScheduledRender.scheduleRender(this,this.#o)}set type(r){this.#i.type=r,t.ScheduledRender.scheduleRender(this,this.#o)}set title(r){this.#i.title=r,t.ScheduledRender.scheduleRender(this,this.#o)}set disabled(r){this.#l(r),t.ScheduledRender.scheduleRender(this,this.#o)}set active(r){this.#i.active=r,t.ScheduledRender.scheduleRender(this,this.#o)}get active(){return this.#i.active}set spinner(r){this.#i.spinner=r,t.ScheduledRender.scheduleRender(this,this.#o)}get jslogContext(){return this.#i.jslogContext}set jslogContext(r){this.#i.jslogContext=r,t.ScheduledRender.scheduleRender(this,this.#o)}#l(r){this.#i.disabled=r,this.toggleAttribute("disabled",r)}focus(){this.#r.querySelector("button")?.focus()}connectedCallback(){this.#r.adoptedStyleSheets=[s],t.ScheduledRender.scheduleRender(this,this.#o)}#s(r){if(this.#i.disabled)return r.stopPropagation(),void r.preventDefault();this.form&&"submit"===this.#i.type&&(r.preventDefault(),this.form.dispatchEvent(new SubmitEvent("submit",{submitter:this}))),this.form&&"reset"===this.#i.type&&(r.preventDefault(),this.form.reset())}#d(r){const o=r.target,e=o?.assignedNodes();this.#n=!e||!Boolean(e.length),t.ScheduledRender.scheduleRender(this,this.#o)}#c(){return"toolbar"===this.#i.variant||"primary_toolbar"===this.#i.variant}#t(){if(!this.#i.variant)throw new Error("Button requires a variant to be defined");if(this.#c()){if(!this.#i.iconUrl&&!this.#i.iconName)throw new Error("Toolbar button requires an icon");if(!this.#n)throw new Error("Toolbar button does not accept children")}if("round"===this.#i.variant){if(!this.#i.iconUrl&&!this.#i.iconName)throw new Error("Round button requires an icon");if(!this.#n)throw new Error("Round button does not accept children")}if(this.#i.iconName&&this.#i.iconUrl)throw new Error("Both iconName and iconUrl are provided.");const t=Boolean(this.#i.iconUrl)||Boolean(this.#i.iconName),s={primary:"primary"===this.#i.variant,tonal:"tonal"===this.#i.variant,secondary:"secondary"===this.#i.variant,toolbar:this.#c(),"primary-toolbar":"primary_toolbar"===this.#i.variant,round:"round"===this.#i.variant,"text-with-icon":t&&!this.#n,"only-icon":t&&this.#n,"only-text":!t&&!this.#n,small:Boolean("SMALL"===this.#i.size),active:this.#i.active},i={primary:"primary"===this.#i.variant,secondary:"secondary"===this.#i.variant,disabled:Boolean(this.#i.disabled),spinner:!0},n=this.#i.jslogContext&&o.action().track({click:!0}).context(this.#i.jslogContext);r.render(r.html`
        <button title=${r.Directives.ifDefined(this.#i.title)} .disabled=${this.#i.disabled} class=${r.Directives.classMap(s)} jslog=${r.Directives.ifDefined(n)}>
          ${t?r.html`
                <${e.Icon.Icon.litTagName} name=${this.#i.iconName||this.#i.iconUrl}>
                </${e.Icon.Icon.litTagName}>`:""}
          ${this.#i.spinner?r.html`<span class=${r.Directives.classMap(i)}></span>`:""}
          <slot @slotchange=${this.#d}></slot>
        </button>
      `,this.#r,{host:this})}get value(){return this.#i.value||""}set value(r){this.#i.value=r}get form(){return this.#a.form}get name(){return this.getAttribute("name")}get type(){return this.#i.type}get validity(){return this.#a.validity}get validationMessage(){return this.#a.validationMessage}get willValidate(){return this.#a.willValidate}checkValidity(){return this.#a.checkValidity()}reportValidity(){return this.#a.reportValidity()}}customElements.define("devtools-button",i);var n=Object.freeze({__proto__:null,Button:i});export{n as Button};
