import*as e from"../../../../core/i18n/i18n.js";import*as t from"../../legacy.js";const o={openFile:"Open file",runCommand:"Run command"},n=e.i18n.registerUIStrings("ui/legacy/components/quick_open/quick_open-meta.ts",o),i=e.i18n.getLazilyComputedLocalizedString.bind(void 0,n);let a;async function s(){return a||(a=await import("./quick_open.js")),a}t.ActionRegistration.registerActionExtension({actionId:"quick-open.show-command-menu",category:"GLOBAL",title:i(o.runCommand),loadActionDelegate:async()=>new((await s()).CommandMenu.ShowActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+P",keybindSets:["devToolsDefault","vsCode"]},{shortcut:"F1",keybindSets:["vsCode"]}]}),t.ActionRegistration.registerActionExtension({actionId:"quick-open.show",category:"GLOBAL",title:i(o.openFile),loadActionDelegate:async()=>new((await s()).QuickOpen.ShowActionDelegate),order:100,bindings:[{platform:"mac",shortcut:"Meta+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+O",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+P",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+O",keybindSets:["devToolsDefault","vsCode"]}]}),t.ContextMenu.registerItem({location:"mainMenu/default",actionId:"quick-open.show-command-menu",order:void 0}),t.ContextMenu.registerItem({location:"mainMenu/default",actionId:"quick-open.show",order:void 0});
