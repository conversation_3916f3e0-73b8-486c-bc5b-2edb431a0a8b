{"name": "cpan213-group7-eventmate", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/cli": "^0.24.20", "@expo/vector-icons": "^14.0.2", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.4", "expo": "~53.0.0", "react": "18.3.1", "react-native": "0.76.3", "react-native-safe-area-context": "~4.12.0", "react-native-screens": "~4.0.0", "react-native-vector-icons": "^10.1.0", "react-redux": "^9.1.2"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}