import*as e from"../../../../core/i18n/i18n.js";import*as t from"../../../../services/window_bounds/window_bounds.js";import*as i from"../../theme_support/theme_support.js";import*as s from"../../../../core/common/common.js";import*as r from"../../../../core/platform/platform.js";import*as n from"../../../components/render_coordinator/render_coordinator.js";import*as o from"../../legacy.js";import*as a from"../../../../core/root/root.js";import*as l from"../../../../models/trace/trace.js";import*as h from"../../../../core/host/host.js";import*as d from"../../../visual_logging/visual_logging.js";import*as c from"../../../../core/sdk/sdk.js";import*as u from"../../../../models/bindings/bindings.js";import*as m from"../../../../models/workspace/workspace.js";import"../source_frame/source_frame.js";import*as g from"../../../components/icon_button/icon_button.js";import*as p from"../../../lit-html/lit-html.js";const f={congrats:"Congrats, you win!",ps:"PS: You can also open the game by typing `fixme`"},v=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/BrickBreaker.ts",f),w=e.i18n.getLocalizedString.bind(void 0,v),b=15,y=10,T=[{light:"rgb(224,240,255)",mediumLighter:"rgb(176,208,255)",mediumDarker:"rgb(112,160,221)",dark:"rgb(0,92,153)"},{light:"rgb(253, 216, 229)",mediumLighter:"rgb(250, 157, 188)",mediumDarker:"rgb(249, 98, 154)",dark:"rgb(254, 5, 105)"},{light:"rgb(254, 234, 234)",mediumLighter:"rgb(255, 216, 216)",mediumDarker:"rgb(255, 195, 195)",dark:"rgb(235, 125, 138)"},{light:"rgb(226,183,206)",mediumLighter:"rgb(219,124,165)",mediumDarker:"rgb(146,60,129)",dark:"rgb(186, 85, 255)"},{light:"rgb(206,255,206)",mediumLighter:"rgb(128,255,128)",mediumDarker:"rgb(0,246,0)",dark:"rgb(0,187,0)"},{light:"rgb(255, 188, 181)",mediumLighter:"rgb(254, 170, 170)",mediumDarker:"rgb(215, 59, 43)",dark:"rgb(187, 37, 23)"},{light:"rgb(236, 254, 250)",mediumLighter:"rgb(204, 255, 245)",mediumDarker:"rgb(164, 240, 233)",dark:"rgb(72,189,144)"},{light:"rgb(255, 225, 185)",mediumLighter:"rgb(255, 204, 141)",mediumDarker:"rgb(240, 140, 115)",dark:"rgb(211, 96, 117)"},{light:"rgb(218, 255, 248)",mediumLighter:"rgb(177, 235, 236)",mediumDarker:"rgb(112, 214, 214)",dark:"rgb(34, 205, 181)"}];class x extends HTMLElement{timelineFlameChart;#e;#t;#i;#s;#r;#n=0;#o=new Set;#a=new Map;#l=this.#h.bind(this);#d=this.#c.bind(this);#u=this.#m.bind(this);#g=this.#p.bind(this);#f=this.#v.bind(this);#w=t.WindowBoundsService.WindowBoundsServiceImpl.instance().getDevToolsBoundingElement();#b=0;#y=!1;#T=devicePixelRatio;#x=0;#E=0;#S=0;#C=0;#L=0;#P=!1;#k=!1;#D=0;#R=0;#I=0;#M=150;#H=150;#B=1500;#O=this.#B-this.#H;#A=0;#G=0;#z;constructor(e){super(),this.timelineFlameChart=e,this.#e=this.createChild("canvas","fill"),this.#t=this.#e.getContext("2d"),this.#i=document.createElement("canvas"),this.#s=this.#i.getContext("2d");const t=Math.floor(Math.random()*T.length);this.#z=T[t],this.#r=this.createChild("div"),this.#r.classList.add("scorePanel"),this.#r.style.borderImage="linear-gradient("+this.#z.mediumDarker+","+this.#z.dark+") 1",this.initButton()}initButton(){const e=this.createChild("div");e.classList.add("game-close-button"),e.innerHTML="<b><span style='font-size: 1.2em; color: white'>x</span></b>",e.style.background=this.#z.dark,e.style.boxShadow=this.#z.dark+" 1px 1px, "+this.#z.mediumDarker+" 3px 3px, "+this.#z.mediumLighter+" 5px 5px",e.addEventListener("click",this.#p.bind(this)),this.appendChild(e)}connectedCallback(){this.#y=!0,this.#W(),this.#w.addEventListener("keydown",this.#l),document.addEventListener("keydown",this.#l,!1),document.addEventListener("keyup",this.#d,!1),document.addEventListener("keypress",this.#u,!1),window.addEventListener("resize",this.#g),document.addEventListener("mousemove",this.#f,!1),this.tabIndex=1,this.focus()}disconnectedCallback(){this.#w.removeEventListener("keydown",this.#l),window.removeEventListener("resize",this.#g),document.removeEventListener("keydown",this.#l,!1),document.removeEventListener("keyup",this.#d,!1),window.removeEventListener("resize",this.#g),document.removeEventListener("keypress",this.#u,!1),document.removeEventListener("mousemove",this.#f,!1)}#F(){const e=window.devicePixelRatio,t=Math.round(this.offsetHeight*e),i=Math.round(this.offsetWidth*e);this.#e.height=t,this.#e.width=i,this.#e.style.height=t/e+"px",this.#e.style.width=i/e+"px"}#p(){this.#y=!1,this.remove()}#W(){this.#F(),this.#A=Math.max(.1,(this.offsetHeight-this.#H)/this.#O),this.#G=10*this.#A;const e=this.timelineFlameChart.drawTrackOnCanvas("Main",this.#t,y);if(null===e||0===e.visibleEntries.size)return console.error("Could not draw game"),void this.#p();this.#n=e.top,this.#o=e.visibleEntries,this.#b=this.#n+this.timelineFlameChart.getCanvas().getBoundingClientRect().top-this.timelineFlameChart.getScrollOffset(),requestAnimationFrame((()=>this.#V(e.top,e.height)))}#V(e,t){if(0===e)return void this.#U();const i=window.devicePixelRatio,s=Math.round(e*i),r=Math.max(e-4,0),n=Math.round(r*i),o=this.#e;this.#i.height=o.height,this.#i.width=o.width,this.#i.style.height=o.style.height,this.#i.style.width=o.style.width,this.#s.drawImage(o,0,s,o.width,t*i,0,n,o.width,t*i),this.#F(),this.#t.drawImage(this.#i,0,0),requestAnimationFrame((()=>this.#V(r,t)))}#c(e){"Right"===e.key||"ArrowRight"===e.key||"d"===e.key?(this.#P=!1,e.preventDefault()):"Left"===e.key||"ArrowLeft"===e.key||"a"===e.key?(this.#k=!1,e.preventDefault()):e.stopImmediatePropagation()}#m(e){e.stopImmediatePropagation(),e.preventDefault()}#h(e){"Escape"===e.key?(this.#p(),e.stopImmediatePropagation()):"Right"===e.key||"ArrowRight"===e.key||"d"===e.key?(this.#P=!0,e.preventDefault()):"Left"===e.key||"ArrowLeft"===e.key||"a"===e.key?(this.#k=!0,e.preventDefault()):(e.preventDefault(),e.stopImmediatePropagation())}#v(e){this.#L=Math.max(e.offsetX-this.#M/2,0),this.#L=Math.min(this.#L,this.offsetWidth-this.#M)}#U(){this.#x=this.offsetWidth/2,this.#E=this.offsetHeight-b-y,this.#S=0,this.#C=-Math.SQRT2*this.#G,this.#L=(this.#e.width-this.#M)/2,this.#P=!1,this.#k=!1,this.#D=this.timelineFlameChart.getBarHeight(),this.#I=this.#o.size,this.#R=Math.max(Math.round(this.#I/17),2),this.#N()}#_(){this.#x=this.offsetWidth/2,this.#E=this.offsetHeight-b-y,this.#S=0,this.#C=-Math.SQRT2*this.#G}#X(){if(!this.#t)return;const e=this.#t.createRadialGradient(this.#x+2.5,this.#E-2.5,0,this.#x+2.5,this.#E-2.5,y);e.addColorStop(.3,this.#z.mediumLighter),e.addColorStop(.6,this.#z.mediumDarker),e.addColorStop(1,this.#z.dark),this.#t.beginPath(),this.#t.arc(this.#x,this.#E,y,0,2*Math.PI),this.#t.fillStyle=e,this.#t.fill(),this.#t.closePath()}#$(){if(!this.#t)return;const e=this.#t.createRadialGradient(this.#L+this.#M/3,this.offsetHeight-b-3.75,0,this.#L+this.#M/3,this.offsetHeight-b-3.75,this.#M/2);e.addColorStop(.3,this.#z.dark),e.addColorStop(1,this.#z.mediumDarker),this.#t.beginPath(),this.#t.rect(this.#L,this.offsetHeight-b,this.#M,b),this.#t.fillStyle=e,this.#t.fill(),this.#t.closePath()}#j(){if(this.#t)for(const e of this.#a.values())this.#t.beginPath(),this.#t.rect(e.x,e.y,e.width+.5,this.#D+.5),this.#t.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-container",this),this.#t.fill(),this.#t.closePath()}#N(){if(this.#T!==devicePixelRatio&&(this.#y=!1),0===this.#R)return window.alert("GAME OVER"),void this.#p();if(0===this.#I)return void this.#Y();this.#t.clearRect(0,0,this.#e.width,this.#e.height),this.#t.drawImage(this.#i,0,0),this.#t.save(),this.#t.scale(devicePixelRatio,devicePixelRatio),this.#s.save(),this.#s.scale(devicePixelRatio,devicePixelRatio),this.#j(),this.#X(),this.#$(),this.#K();const e=`<div><b><span style='font-size: 1.3em; color:  ${this.#z.dark}'>&#x2764;&#xfe0f; ${this.#R}</span></b></div>`,t=`<div><b><span style='font-size: 1.3em; color: ${this.#z.dark}'> 🧱 ${this.#I}</span></b></div>`;if(this.#r.innerHTML=e+t,this.#I=this.#o.size-this.#a.size,this.#G=(10+6*this.#a.size/this.#o.size)*this.#A,this.#M=150-65*this.#a.size/this.#o.size,(this.#x+this.#S>this.offsetWidth-y||this.#x+this.#S<y)&&(this.#S=-this.#S),this.#E+this.#C<y)this.#C=-this.#C;else if(this.#E+this.#C>this.offsetHeight-y&&this.#C>0)if(this.#x>this.#L-y&&this.#x<this.#L+this.#M+y){let e=Math.min(this.#x,this.#L+this.#M);e=Math.max(e,this.#L);const t=(e-this.#L)*this.#G*2/this.#M;this.#S=-this.#G+t,this.#C=-Math.sqrt(2*Math.pow(this.#G,2)-Math.pow(this.#S,2))}else this.#_(),this.#L=(this.offsetWidth-this.#M)/2,this.#R--;const i=Math.round(this.clientWidth/60);this.#P&&this.#L<this.offsetWidth-this.#M?this.#L+=i:this.#k&&this.#L>0&&(this.#L-=i),this.#x+=Math.round(this.#S),this.#E+=Math.round(this.#C),this.#t.restore(),this.#s.restore(),this.#y&&requestAnimationFrame(this.#N.bind(this))}#K(){const e=this.timelineFlameChart.getCanvas().getBoundingClientRect(),t=this.#E+this.#b-e.top,i=this.timelineFlameChart.coordinatesToEntryIndex(this.#x,t+y),s=this.timelineFlameChart.coordinatesToEntryIndex(this.#x,t-y),r=this.timelineFlameChart.coordinatesToEntryIndex(this.#x+y,t),n=this.timelineFlameChart.coordinatesToEntryIndex(this.#x-y,t),o=y/Math.SQRT2,a=this.timelineFlameChart.coordinatesToEntryIndex(this.#x+o,t+o),l=this.timelineFlameChart.coordinatesToEntryIndex(this.#x-o,t+o),h=this.timelineFlameChart.coordinatesToEntryIndex(this.#x+o,t-o),d=this.timelineFlameChart.coordinatesToEntryIndex(this.#x-o,t-o),c=t=>{const i=this.timelineFlameChart.entryIndexToCoordinates(t);if(i){const s=Math.max(i.x-e.left,0);this.#a.set(t,{x:s-.5,y:i.y-this.#b-.5,width:this.timelineFlameChart.entryWidth(t)})}};if(i>-1&&!this.#a.has(i)&&this.#o.has(i))return this.#C=-this.#C,void c(i);if(s>-1&&!this.#a.has(s)&&this.#o.has(s))return this.#C=-this.#C,void c(s);if(r>-1&&!this.#a.has(r)&&this.#o.has(r))return this.#S=-this.#S,void c(r);if(n>-1&&!this.#a.has(n)&&this.#o.has(n))return this.#S=-this.#S,void c(n);const u=[a,l,h,d];for(const e of u)if(e>-1&&!this.#a.has(e)&&this.#o.has(e))return this.#S=-this.#S,this.#C=-this.#C,void c(e)}#Z(e,t){return Math.floor(Math.random()*(t-e)+e)}#Y(){this.#F();let e=0;const t=this.offsetWidth/15,i=.7*this.offsetHeight/15,s=[],r=()=>40*Math.random()-20,n=()=>{for(let e=0;e<75;e++){const n=document.createElement("span");n.className="confetti-100",n.append(this.#q(e%15*t+r(),e%5*i+r())),s.push(window.setTimeout((()=>this.append(n)),100*Math.random())),s.push(window.setTimeout((()=>{n.remove()}),1e3))}++e<6?setTimeout(n,100*Math.random()+400):(window.alert(`${w(f.congrats)}\n${w(f.ps)}`),s.forEach((e=>clearTimeout(e))),this.#p())};n()}#q(e,t){const i=400,s=["💯","🎉","🎊"],r=document.createElement("span");return r.textContent=s[this.#Z(0,s.length)],r.className="confetti-100-particle",r.style.setProperty("--rotation",this.#Z(-1080,1080)+"deg"),r.style.setProperty("--to-X",this.#Z(-400,i)+"px"),r.style.setProperty("--to-Y",this.#Z(-400,i)+"px"),r.style.left=e+"px",r.style.top=t+"px",r}}customElements.define("brick-breaker",x);var E=Object.freeze({__proto__:null,BrickBreaker:x}),S={cssContent:".chart-viewport-v-scroll{position:absolute;top:0;right:0;bottom:0;overflow-x:hidden;z-index:200;padding-left:1px}.chart-viewport-v-scroll.always-show-scrollbar{overflow-y:scroll}:host-context(.platform-mac) .chart-viewport-v-scroll{right:2px;top:3px;bottom:3px}:host-context(.platform-mac) ::-webkit-scrollbar{width:8px}:host-context(.platform-mac) ::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-mac);border-radius:50px}:host-context(.platform-mac) .chart-viewport-v-scroll:hover::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-mac-hover)}:host-context(.overlay-scrollbar-enabled) ::-webkit-scrollbar{width:10px}:host-context(.overlay-scrollbar-enabled) ::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-other)}:host-context(.overlay-scrollbar-enabled) .chart-viewport-v-scroll:hover::-webkit-scrollbar-thumb{background-color:var(--color-scrollbar-other-hover)}.chart-viewport-selection-overlay{position:absolute;z-index:100;background-color:var(--sys-color-state-ripple-primary);border-color:var(--sys-color-primary);border-width:0 1px;border-style:solid;pointer-events:none;top:0;bottom:0;text-align:center}.chart-viewport-selection-overlay .time-span{white-space:nowrap;position:absolute;left:0;right:0;bottom:0}"},C={cssContent:'.flame-chart-main-pane{overflow:hidden;--selected-group-background:hsl(215deg 85% 98%);--selected-group-border:hsl(216deg 68% 54%)}:host-context(.-theme-with-dark-background) .flame-chart-main-pane{--selected-group-background:hsl(215deg 85% 15%);--selected-group-border:hsl(216deg 68% 46%)}.flame-chart-marker-highlight-element{position:absolute;top:1px;height:18px;width:6px;margin:0 -3px;content:"";display:block}.flame-chart-canvas:focus-visible{border-top:1px solid var(--sys-color-state-focus-ring);border-bottom:1px solid var(--sys-color-state-focus-ring)}.flame-chart-highlight-element{position:absolute;pointer-events:none;background-color:var(--sys-color-state-hover-on-subtle)}.reveal-descendants-arrow-highlight-element{position:absolute;pointer-events:none;background-color:var(--sys-color-state-hover-on-subtle)}.flame-chart-selected-element{position:absolute;pointer-events:none;outline:2px solid var(--sys-color-primary);background-color:var(--sys-color-state-ripple-primary)}.flame-chart-search-element{position:absolute;pointer-events:none;outline:1px solid var(--sys-color-on-surface-subtle);background-color:var(--sys-color-state-ripple-neutral-on-subtle)}.chart-cursor-element{position:absolute;top:0;bottom:0;z-index:100;width:2px;background-color:var(--sys-color-primary);pointer-events:none}.flame-chart-entry-info:not(:empty){z-index:2000;position:absolute;background-color:var(--sys-color-cdt-base-container);pointer-events:none;padding:4px 8px;white-space:nowrap;max-width:80%;box-shadow:var(--drop-shadow)}.flame-chart-entry-info table tr td:empty{padding:0}.flame-chart-entry-info table tr td:not(:empty){padding:0 5px;white-space:nowrap}.flame-chart-entry-info table tr td:first-child{font-weight:bold}.flame-chart-entry-info table tr td span{margin-right:5px}'};let L=null;function P(){if(L)return L;const e=getComputedStyle(document.body);return L=e.fontFamily?e.fontFamily:h.Platform.fontFamily(),L}const k="11px";var D=Object.freeze({__proto__:null,getFontFamilyForCanvas:P,DEFAULT_FONT_SIZE:k}),R={cssContent:".resources-dividers{position:absolute;left:0;right:0;top:0;z-index:-100;bottom:0}.resources-event-dividers{position:absolute;left:0;right:0;height:100%;top:0;z-index:300;pointer-events:none}.resources-dividers-label-bar{position:absolute;top:0;left:0;right:0;background-clip:padding-box;height:20px;z-index:200;pointer-events:none;overflow:hidden}.resources-divider{position:absolute;width:1px;top:0;bottom:0;background-color:var(--sys-color-divider)}.resources-event-divider{position:absolute;width:1px;top:0;bottom:0;z-index:300}.resources-divider-label{position:absolute;top:4px;right:3px;font-size:80%;white-space:nowrap;pointer-events:none}.timeline-grid-header{height:20px;pointer-events:none}"};const I=new Map;class M{element;dividersElementInternal;gridHeaderElement;eventDividersElement;dividersLabelBarElementInternal;constructor(){this.element=document.createElement("div"),i.ThemeSupport.instance().appendStyle(this.element,R),this.dividersElementInternal=this.element.createChild("div","resources-dividers"),this.gridHeaderElement=document.createElement("div"),this.gridHeaderElement.classList.add("timeline-grid-header"),this.eventDividersElement=this.gridHeaderElement.createChild("div","resources-event-dividers"),this.dividersLabelBarElementInternal=this.gridHeaderElement.createChild("div","resources-dividers-label-bar"),this.element.appendChild(this.gridHeaderElement)}static calculateGridOffsets(e,t){const i=e.computePosition(e.maximumBoundary());let s=i/64,r=e.boundarySpan()/s;const n=i/e.boundarySpan(),o=Math.ceil(Math.log(r)/Math.LN10);r=Math.pow(10,o),r*n>=320&&(r/=5),r*n>=128&&(r/=2);const a=Math.ceil((e.minimumBoundary()-e.zeroTime())/r)*r+e.zeroTime();let l=e.maximumBoundary();l+=64/n,s=Math.ceil((l-a)/r),r||(s=0);const h=[];for(let i=0;i<s;++i){const s=a+100*r*i/100,n=e.computePosition(s);n<(t||0)||h.push({position:Math.floor(n),time:s})}return{offsets:h,precision:Math.max(0,-Math.floor(Math.log(1.01*r)/Math.LN10))}}static drawCanvasGrid(e,t){e.save(),e.scale(window.devicePixelRatio,window.devicePixelRatio);const i=Math.floor(e.canvas.height/window.devicePixelRatio);e.strokeStyle=getComputedStyle(document.body).getPropertyValue("--app-color-strokestyle"),e.lineWidth=1,e.translate(.5,.5),e.beginPath();for(const s of t.offsets)e.moveTo(s.position,0),e.lineTo(s.position,i);e.stroke(),e.restore()}static drawCanvasHeaders(e,t,s,r,n,o){e.save(),e.scale(window.devicePixelRatio,window.devicePixelRatio);const a=Math.ceil(e.canvas.width/window.devicePixelRatio);e.beginPath(),e.fillStyle=i.ThemeSupport.instance().getComputedValue("--color-background-opacity-50"),e.fillRect(0,0,a,n),e.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-on-surface"),e.textBaseline="hanging",e.font=`${k} ${P()}`;for(const i of t.offsets){const t=s(i.time),n=e.measureText(t).width,a=i.position-n-4;(!o||o<a)&&e.fillText(t,a,r)}e.restore()}get dividersElement(){return this.dividersElementInternal}get dividersLabelBarElement(){return this.dividersLabelBarElementInternal}removeDividers(){this.dividersElementInternal.removeChildren(),this.dividersLabelBarElementInternal.removeChildren()}updateDividers(e,t){const i=M.calculateGridOffsets(e,t),s=i.offsets,r=i.precision,n=this.dividersElementInternal.clientWidth;let o=this.dividersElementInternal.firstChild,a=this.dividersLabelBarElementInternal.firstChild;for(let t=0;t<s.length;++t){if(!o){o=document.createElement("div"),o.className="resources-divider",this.dividersElementInternal.appendChild(o),a=document.createElement("div"),a.className="resources-divider";const e=document.createElement("div");e.className="resources-divider-label",I.set(a,e),a.appendChild(e),this.dividersLabelBarElementInternal.appendChild(a)}const i=s[t].time,l=s[t].position;if(a){const t=I.get(a);t&&(t.textContent=e.formatValue(i,r))}const h=100*l/n;o.style.left=h+"%",a&&(a.style.left=h+"%"),o=o.nextSibling,a&&(a=a.nextSibling)}for(;o;){const e=o.nextSibling;if(this.dividersElementInternal.removeChild(o),!e)break;o=e}for(;a;){const e=a.nextSibling;if(this.dividersLabelBarElementInternal.removeChild(a),!e)break;a=e}return!0}addEventDivider(e){this.eventDividersElement.appendChild(e)}addEventDividers(e){this.gridHeaderElement.removeChild(this.eventDividersElement);for(const t of e)this.eventDividersElement.appendChild(t);this.gridHeaderElement.appendChild(this.eventDividersElement)}removeEventDividers(){this.eventDividersElement.removeChildren()}hideEventDividers(){this.eventDividersElement.classList.add("hidden")}showEventDividers(){this.eventDividersElement.classList.remove("hidden")}hideDividers(){this.dividersElementInternal.classList.add("hidden")}showDividers(){this.dividersElementInternal.classList.remove("hidden")}setScrollTop(e){this.dividersLabelBarElementInternal.style.top=e+"px",this.eventDividersElement.style.top=e+"px"}}var H=Object.freeze({__proto__:null,TimelineGrid:M});const B={flameChart:"Flame Chart",sHovered:"{PH1} hovered",sSelected:"{PH1} selected",sExpanded:"{PH1} expanded",sCollapsed:"{PH1} collapsed",hideFunction:"Hide function",hideChildren:"Hide children",hideRepeatingChildren:"Hide repeating children",resetChildren:"Reset children",resetTrace:"Reset trace"},O=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/FlameChart.ts",B),A=e.i18n.getLocalizedString.bind(void 0,O);class G extends(s.ObjectWrapper.eventMixin(o.Widget.VBox)){groupExpansionSetting;groupExpansionState;groupHiddenState;flameChartDelegate;chartViewport;dataProvider;candyStripePattern;contextMenu;viewportElement;canvas;entryInfo;markerHighlighElement;highlightElement;revealDescendantsArrowHighlightElement;selectedElement;rulerEnabled;barHeight;hitMarginPx;textBaseline;textPadding;headerLeftPadding;arrowSide;expansionArrowIndent;headerLabelXPadding;headerLabelYPadding;highlightedMarkerIndex;highlightedEntryIndex;selectedEntryIndex;rawTimelineDataLength;markerPositions;lastMouseOffsetX;selectedGroupIndex;keyboardFocusedGroup;offsetWidth;offsetHeight;dragStartX;dragStartY;lastMouseOffsetY;minimumBoundaryInternal;maxDragOffset;timelineLevels;visibleLevelOffsets;visibleLevels;visibleLevelHeights;groupOffsets;rawTimelineData;forceDecorationCache;entryColorsCache;totalTime;#Q;#J;#ee;#te=[];constructor(e,t,s){super(!0),this.#Q=`${k} ${P()}`,this.registerRequiredCSS(C),this.contentElement.classList.add("flame-chart-main-pane"),this.groupExpansionSetting=s,this.groupExpansionState=s&&s.get()||{},this.groupHiddenState={},this.flameChartDelegate=t,this.chartViewport=new _(this),this.chartViewport.show(this.contentElement),this.dataProvider=e,this.viewportElement=this.chartViewport.viewportElement,this.canvas=this.viewportElement.createChild("canvas","fill"),this.candyStripePattern=null,this.canvas.tabIndex=0,o.ARIAUtils.setLabel(this.canvas,A(B.flameChart)),o.ARIAUtils.markAsTree(this.canvas),this.setDefaultFocusedElement(this.canvas),this.canvas.classList.add("flame-chart-canvas"),this.canvas.addEventListener("mousemove",this.onMouseMove.bind(this),!1),this.canvas.addEventListener("mouseout",this.onMouseOut.bind(this),!1),this.canvas.addEventListener("click",this.onClick.bind(this),!1),this.canvas.addEventListener("keydown",this.onKeyDown.bind(this),!1),a.Runtime.experiments.isEnabled("track-context-menu")&&this.canvas.addEventListener("contextmenu",this.onContextMenu.bind(this),!1),this.entryInfo=this.viewportElement.createChild("div","flame-chart-entry-info"),this.markerHighlighElement=this.viewportElement.createChild("div","flame-chart-marker-highlight-element"),this.highlightElement=this.viewportElement.createChild("div","flame-chart-highlight-element"),this.revealDescendantsArrowHighlightElement=this.viewportElement.createChild("div","reveal-descendants-arrow-highlight-element"),this.selectedElement=this.viewportElement.createChild("div","flame-chart-selected-element"),this.canvas.addEventListener("focus",(()=>{this.dispatchEventToListeners("CanvasFocused")}),!1),o.UIUtils.installDragHandle(this.viewportElement,this.startDragging.bind(this),this.dragging.bind(this),this.endDragging.bind(this),null),this.rulerEnabled=!0,this.barHeight=17,this.hitMarginPx=3,this.textBaseline=5,this.textPadding=5,this.chartViewport.setWindowTimes(e.minimumBoundary(),e.minimumBoundary()+e.totalTime()),this.headerLeftPadding=6,this.arrowSide=8,this.expansionArrowIndent=this.headerLeftPadding+this.arrowSide/2,this.headerLabelXPadding=3,this.headerLabelYPadding=2,this.highlightedMarkerIndex=-1,this.highlightedEntryIndex=-1,this.selectedEntryIndex=-1,this.#ee=-1,this.rawTimelineDataLength=0,this.markerPositions=new Map,this.lastMouseOffsetX=0,this.selectedGroupIndex=-1,this.keyboardFocusedGroup=-1,i.ThemeSupport.instance().addEventListener(i.ThemeChangeEvent.eventName,(()=>{this.scheduleUpdate()}))}willHide(){this.hideHighlight()}getBarHeight(){return this.barHeight}setBarHeight(e){this.barHeight=e}setTextBaseline(e){this.textBaseline=e}setTextPadding(e){this.textPadding=e}enableRuler(e){this.rulerEnabled=e}alwaysShowVerticalScroll(){this.chartViewport.alwaysShowVerticalScroll()}disableRangeSelection(){this.chartViewport.disableRangeSelection()}highlightEntry(e){this.highlightedEntryIndex!==e&&this.dataProvider.entryColor(e)&&(this.highlightedEntryIndex=e,this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.dispatchEventToListeners("EntryHighlighted",e))}highlightAllEntries(e){for(const t of e){const e=this.viewportElement.createChild("div","flame-chart-search-element");this.#te.push(e),e.id=t.toString(),this.updateElementPosition(e,t)}}removeSearchResultHighlights(){for(const e of this.#te)e.remove();this.#te=[]}hideHighlight(){-1===this.#ee&&this.entryInfo.removeChildren(),-1!==this.highlightedEntryIndex&&(this.highlightedEntryIndex=-1,this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.dispatchEventToListeners("EntryHighlighted",-1))}createCandyStripePattern(){const e=17,t=document.createElement("canvas");t.width=e,t.height=e;const i=t.getContext("2d");i.translate(8.5,8.5),i.rotate(.25*Math.PI),i.translate(-8.5,-8.5),i.fillStyle="rgba(255, 0, 0, 0.8)";for(let e=-17;e<34;e+=3)i.fillRect(e,-17,1,51);return i.createPattern(t,"repeat")}resetCanvas(){const e=window.devicePixelRatio,t=Math.round(this.offsetWidth*e),i=Math.round(this.offsetHeight*e);this.canvas.width=t,this.canvas.height=i,this.canvas.style.width=t/e+"px",this.canvas.style.height=i/e+"px"}windowChanged(e,t,i){this.flameChartDelegate.windowChanged(e,t,i)}updateRangeSelection(e,t){this.flameChartDelegate.updateRangeSelection(e,t)}setSize(e,t){this.offsetWidth=e,this.offsetHeight=t}startDragging(e){return this.hideHighlight(),this.maxDragOffset=0,this.dragStartX=e.pageX,this.dragStartY=e.pageY,!0}dragging(e){const t=e.pageX-this.dragStartX,i=e.pageY-this.dragStartY;this.maxDragOffset=Math.max(this.maxDragOffset,Math.sqrt(t*t+i*i))}endDragging(e){this.updateHighlight()}timelineData(e){if(!this.dataProvider)return null;const t=this.dataProvider.timelineData(e);return(t!==this.rawTimelineData||t&&t.entryStartTimes.length!==this.rawTimelineDataLength)&&this.processTimelineData(t),this.rawTimelineData||null}revealEntry(e){const t=this.timelineData();if(!t)return;const i=this.chartViewport.windowLeftTime(),s=this.chartViewport.windowRightTime(),r=t.entryStartTimes[e],n=t.entryTotalTimes[e],o=r+n;let a=Math.min(n,s-i);const l=t.entryLevels[e];this.chartViewport.setScrollOffset(this.levelToOffset(l),this.levelHeight(l));const h=(s-i)/this.offsetWidth;if(a=Math.max(a,30*h),i>o){const e=i-o+a;this.windowChanged(i-e,s-e,!0)}else if(s<r){const e=r-s+a;this.windowChanged(i+e,s+e,!0)}}setWindowTimes(e,t,i){this.chartViewport.setWindowTimes(e,t,i),this.updateHighlight()}onMouseMove(e){this.#ee=-1;const t=e;if(this.lastMouseOffsetX=t.offsetX,this.lastMouseOffsetY=t.offsetY,this.enabled()&&!this.chartViewport.isDragging())return this.coordinatesToGroupIndex(t.offsetX,t.offsetY,!0)>=0?(this.hideHighlight(),void(this.viewportElement.style.cursor="pointer")):void this.updateHighlight()}updateHighlight(){const e=this.coordinatesToEntryIndex(this.lastMouseOffsetX,this.lastMouseOffsetY);if(this.updateHiddenChildrenArrowHighlighPosition(e),-1!==e)this.chartViewport.isDragging()||(this.updatePopover(e),this.viewportElement.style.cursor=this.dataProvider.canJumpToEntry(e)?"pointer":"default",this.highlightEntry(e));else{this.hideHighlight();const e=this.coordinatesToGroupIndex(this.lastMouseOffsetX,this.lastMouseOffsetY,!1);e>=0&&this.rawTimelineData&&this.rawTimelineData.groups&&this.rawTimelineData.groups[e].selectable?this.viewportElement.style.cursor="pointer":this.viewportElement.style.cursor="default"}}onMouseOut(){this.lastMouseOffsetX=-1,this.lastMouseOffsetY=-1,this.hideHighlight()}showPopoverForSearchResult(e){this.#ee=e,this.updatePopover(e)}updatePopover(e){this.entryInfo.removeChildren();const t=this.timelineData();if(!t)return;const i=t.groups.at(this.selectedGroupIndex),s=this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e)&&i?this.dataProvider.prepareHighlightedHiddenEntriesArrowInfo&&this.dataProvider.prepareHighlightedHiddenEntriesArrowInfo(i,e):this.dataProvider.prepareHighlightedEntryInfo(e);s&&(this.entryInfo.appendChild(s),this.updatePopoverOffset())}updatePopoverOffset(){let e=this.lastMouseOffsetX,t=this.lastMouseOffsetY;if(-1!==this.#ee){const i=this.entryIndexToCoordinates(this.selectedEntryIndex),{x:s,y:r}=this.canvas.getBoundingClientRect();e=i?.x?i.x-s:e,t=i?.y?i.y-r:t}const i=this.entryInfo.parentElement?this.entryInfo.parentElement.clientWidth:0,s=this.entryInfo.parentElement?this.entryInfo.parentElement.clientHeight:0,n=this.entryInfo.clientWidth,o=this.entryInfo.clientHeight;let a,l;for(let h=0;h<4;++h){const d=2&h?-10-n:10,c=1&h?-6-o:6;if(a=r.NumberUtilities.clamp(e+d,0,i-n),l=r.NumberUtilities.clamp(t+c,0,s-o),a>=e||e>=a+n||l>=t||t>=l+o)break}this.entryInfo.style.left=a+"px",this.entryInfo.style.top=l+"px"}onClick(e){const t=e;this.focus();if(this.maxDragOffset>5)return;this.selectGroup(this.coordinatesToGroupIndex(t.offsetX,t.offsetY,!1)),this.toggleGroupExpand(this.coordinatesToGroupIndex(t.offsetX,t.offsetY,!0));const i=this.timelineData();if(t.shiftKey&&-1!==this.highlightedEntryIndex&&i){const e=i.entryStartTimes[this.highlightedEntryIndex],t=e+i.entryTotalTimes[this.highlightedEntryIndex];this.chartViewport.setRangeSelection(e,t)}else this.chartViewport.onClick(t),this.dispatchEventToListeners("EntryInvoked",this.highlightedEntryIndex)}selectGroup(e){if(e<0||this.selectedGroupIndex===e)return;if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups;if(!t)return;this.keyboardFocusedGroup=e,this.scrollGroupIntoView(e);const i=t[e].name;t[e].selectable?(this.selectedGroupIndex=e,this.flameChartDelegate.updateSelectedGroup(this,t[e]),this.resetCanvas(),this.draw(),o.ARIAUtils.alert(A(B.sSelected,{PH1:i}))):(this.deselectAllGroups(),o.ARIAUtils.alert(A(B.sHovered,{PH1:i})))}deselectAllGroups(){this.selectedGroupIndex=-1,this.flameChartDelegate.updateSelectedGroup(this,null),this.resetCanvas(),this.draw()}deselectAllEntries(){this.selectedEntryIndex=-1,this.rawTimelineData?.resetFlowData(),this.resetCanvas(),this.draw()}isGroupFocused(e){return e===this.selectedGroupIndex||e===this.keyboardFocusedGroup}scrollGroupIntoView(e){if(e<0)return;if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups,i=this.groupOffsets;if(!i||!t)return;const s=i[e];let r=i[e+1];e===t.length-1&&(r+=t[e].style.padding);const n=0===e?0:s,o=Math.min(r-n,this.chartViewport.chartHeight());this.chartViewport.setScrollOffset(n,o)}toggleGroupExpand(e){e<0||!this.isGroupCollapsible(e)||this.rawTimelineData&&this.rawTimelineData.groups&&this.expandGroup(e,!this.rawTimelineData.groups[e].expanded)}expandGroup(e,t=!0,i=!1){if(e<0||!this.isGroupCollapsible(e))return;if(!this.rawTimelineData)return;const s=this.rawTimelineData.groups;if(!s)return;const r=s[e];if(r.expanded=t,this.groupExpansionState[r.name]=r.expanded,this.groupExpansionSetting&&this.groupExpansionSetting.set(this.groupExpansionState),this.updateLevelPositions(),this.updateHighlight(),!r.expanded){const t=this.timelineData();if(t){const i=t.entryLevels[this.selectedEntryIndex];this.selectedEntryIndex>=0&&i>=r.startLevel&&(e>=s.length-1||s[e+1].startLevel>i)&&(this.selectedEntryIndex=-1,this.rawTimelineData.resetFlowData())}}if(this.updateHeight(),this.resetCanvas(),this.draw(),this.scrollGroupIntoView(e),!i){const t=s[e].name,i=r.expanded?A(B.sExpanded,{PH1:t}):A(B.sCollapsed,{PH1:t});o.ARIAUtils.alert(i)}}moveGroupUp(e){if(!(e<0)&&this.rawTimelineData&&this.rawTimelineData.groups&&this.#J){for(let t=0;t<this.#J.children.length;t++){const i=this.#J.children[t];if(i.index===e&&t>=1){this.#J.children[t]=this.#J.children[t-1],this.#J.children[t-1]=i;break}}this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.resetCanvas(),this.draw()}}moveGroupDown(e){if(!(e<0)&&this.rawTimelineData&&this.rawTimelineData.groups&&this.#J){for(let t=0;t<this.#J.children.length;t++){const i=this.#J.children[t];if(i.index===e&&t<=this.#J.children.length-2){this.#J.children[t]=this.#J.children[t+1],this.#J.children[t+1]=i;break}}this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.resetCanvas(),this.draw()}}hideGroup(e){this.#ie(e,!0)}showGroup(e){this.#ie(e,!1)}#ie(e,t){if(e<0)return;if(!this.rawTimelineData||!this.rawTimelineData.groups)return;const i=this.rawTimelineData.groups;if(!i)return;const s=i[e];s.hidden=t,this.groupHiddenState[s.name]=s.hidden,this.updateLevelPositions(),this.updateHighlight(),this.updateHeight(),this.resetCanvas(),this.draw()}modifyTree(e,t){const i=this.timelineData();if(!i)return;const s=i.groups.at(this.selectedGroupIndex);s&&s.expanded&&s.showStackContextMenu&&(this.dataProvider.modifyTree?.(s,t,e),this.dataProvider.timelineData(!0),this.update())}getPossibleActions(){const e=this.timelineData();if(!e)return;const t=e.groups.at(this.selectedGroupIndex);return t&&t.expanded&&t.showStackContextMenu?this.dataProvider.findPossibleContextMenuActions?.(t,this.selectedEntryIndex):void 0}onContextMenu(e){if(-1===this.highlightedEntryIndex)return;this.dispatchEventToListeners("EntryInvoked",this.highlightedEntryIndex),this.setSelectedEntry(this.highlightedEntryIndex);const t=this.getPossibleActions();if(t){if(this.contextMenu=new o.ContextMenu.ContextMenu(e,{useSoftMenu:!0}),t?.MERGE_FUNCTION){this.contextMenu.defaultSection().appendItem(A(B.hideFunction),(()=>{this.modifyTree("MERGE_FUNCTION",this.selectedEntryIndex)}),{jslogContext:"hide-function"}).setShortcut("H")}if(t?.COLLAPSE_FUNCTION){this.contextMenu.defaultSection().appendItem(A(B.hideChildren),(()=>{this.modifyTree("COLLAPSE_FUNCTION",this.selectedEntryIndex)}),{jslogContext:"hide-children"}).setShortcut("C")}if(t?.COLLAPSE_REPEATING_DESCENDANTS){this.contextMenu.defaultSection().appendItem(A(B.hideRepeatingChildren),(()=>{this.modifyTree("COLLAPSE_REPEATING_DESCENDANTS",this.selectedEntryIndex)}),{jslogContext:"hide-repeating-children"}).setShortcut("R")}if(t?.RESET_CHILDREN){this.contextMenu.defaultSection().appendItem(A(B.resetChildren),(()=>{this.modifyTree("RESET_CHILDREN",this.selectedEntryIndex)}),{jslogContext:"reset-children"}).setShortcut("U")}this.contextMenu.defaultSection().appendItem(A(B.resetTrace),(()=>{this.modifyTree("UNDO_ALL_ACTIONS",this.selectedEntryIndex)}),{disabled:!t?.UNDO_ALL_ACTIONS,jslogContext:"reset-trace"}),this.contextMenu.show()}}handleFlameChartTransformEvent(e){if(-1===this.selectedEntryIndex)return;const t=this.getPossibleActions();if(!t)return;const i=e;let s=!1;"KeyH"===i.code&&t.MERGE_FUNCTION?(this.modifyTree("MERGE_FUNCTION",this.selectedEntryIndex),s=!0):"KeyC"===i.code&&t.COLLAPSE_FUNCTION?(this.modifyTree("COLLAPSE_FUNCTION",this.selectedEntryIndex),s=!0):"KeyR"===i.code&&t.COLLAPSE_REPEATING_DESCENDANTS?(this.modifyTree("COLLAPSE_REPEATING_DESCENDANTS",this.selectedEntryIndex),s=!0):"KeyU"===i.code&&(this.modifyTree("RESET_CHILDREN",this.selectedEntryIndex),s=!0),s&&i.consume(!0)}onKeyDown(e){if(!o.KeyboardShortcut.KeyboardShortcut.hasNoModifiers(e)||!this.timelineData())return;let t=this.handleSelectionNavigation(e);!t&&this.rawTimelineData&&this.rawTimelineData.groups&&(t=this.handleKeyboardGroupNavigation(e)),t||this.handleFlameChartTransformEvent(e)}bindCanvasEvent(e,t){this.canvas.addEventListener(e,t)}drawTrackOnCanvas(e,t,i){const s=this.timelineData();if(!s)return null;const r=this.offsetWidth,n=this.offsetHeight;t.save();const o=window.devicePixelRatio;t.scale(o,o),t.fillStyle="rgba(0, 0, 0, 0)",t.fillRect(0,0,r,n),t.font=this.#Q;const a=this.rawTimelineData?.groups||[],l=this.groupOffsets;if(!a.length||!l)return null;const h=a.findIndex((t=>t.name.includes(e)));if(h<0)return null;this.scrollGroupIntoView(h);const d=a[h].startLevel,c=a[h+1].startLevel,u=l[h],m=l[h+1],{colorBuckets:g,titleIndices:p}=this.getDrawableData(t,s),f=e=>{const t=Math.min(this.#se(s,e),r);return s.entryLevels[e]>=d&&s.entryLevels[e]<c&&t>i};let v=[];for(const[e,{indexes:i}]of g){const r=i.filter(f);v=[...v,...r],this.#re(t,s,e,r)}const w=p.filter(f);return this.drawEventTitles(t,s,w,r),t.restore(),{top:l[h],height:m-u,visibleEntries:new Set(v)}}handleKeyboardGroupNavigation(e){const t=e;let i=!1,s=!1;return"ArrowUp"===t.code?i=this.selectPreviousGroup():"ArrowDown"===t.code?i=this.selectNextGroup():"ArrowLeft"===t.code?this.keyboardFocusedGroup>=0&&(this.expandGroup(this.keyboardFocusedGroup,!1),i=!0):"ArrowRight"===t.code?this.keyboardFocusedGroup>=0&&(this.expandGroup(this.keyboardFocusedGroup,!0),this.selectFirstChild(),i=!0):"Enter"===t.key&&(s=this.selectFirstEntryInCurrentGroup(),i=s),i&&!s&&this.deselectAllEntries(),i&&t.consume(!0),i}selectFirstEntryInCurrentGroup(){if(!this.rawTimelineData)return!1;const e=this.rawTimelineData.groups;if(this.keyboardFocusedGroup<0||!e)return!1;const t=e[this.keyboardFocusedGroup].startLevel;if(t<0)return!1;if(this.keyboardFocusedGroup<e.length-1&&e[this.keyboardFocusedGroup+1].startLevel===t)return!1;if(!this.timelineLevels)return!1;const i=this.timelineLevels[t][0];return this.expandGroup(this.keyboardFocusedGroup,!0),this.setSelectedEntry(i),!0}selectPreviousGroup(){if(this.keyboardFocusedGroup<=0)return!1;const e=this.getGroupIndexToSelect(-1);return this.selectGroup(e),!0}selectNextGroup(){if(!this.rawTimelineData||!this.rawTimelineData.groups)return!1;if(this.keyboardFocusedGroup>=this.rawTimelineData.groups.length-1)return!1;const e=this.getGroupIndexToSelect(1);return this.selectGroup(e),!0}getGroupIndexToSelect(e){if(!this.rawTimelineData||!this.rawTimelineData.groups)throw new Error("No raw timeline data");const t=this.rawTimelineData.groups;let i,s,r=this.keyboardFocusedGroup;do{r+=e,i=this.rawTimelineData.groups[r].name,s=-1!==this.keyboardFocusedGroup&&t[r].style.nestingLevel>t[this.keyboardFocusedGroup].style.nestingLevel}while(r>0&&r<t.length-1&&(!i||s));return r}selectFirstChild(){if(!this.rawTimelineData||!this.rawTimelineData.groups)return;const e=this.rawTimelineData.groups;if(this.keyboardFocusedGroup<0||this.keyboardFocusedGroup>=e.length-1)return;const t=this.keyboardFocusedGroup+1;e[t].style.nestingLevel>e[this.keyboardFocusedGroup].style.nestingLevel&&this.selectGroup(t)}handleSelectionNavigation(e){if(-1===this.selectedEntryIndex)return!1;const t=this.timelineData();if(!t)return!1;function i(e,i){if(!t)throw new Error("No timeline data");const s=t.entryStartTimes[e],r=t.entryStartTimes[i],n=s+t.entryTotalTimes[e];return s<r+t.entryTotalTimes[i]&&r<n}const s=e,n=o.KeyboardShortcut.Keys;if(s.keyCode===n.Left.code||s.keyCode===n.Right.code){const i=t.entryLevels[this.selectedEntryIndex],o=this.timelineLevels?this.timelineLevels[i]:[];let a=r.ArrayUtilities.lowerBound(o,this.selectedEntryIndex,((e,t)=>e-t));return a+=s.keyCode===n.Left.code?-1:1,e.consume(!0),a>=0&&a<o.length&&this.dispatchEventToListeners("EntrySelected",o[a]),!0}if(s.keyCode===n.Up.code||s.keyCode===n.Down.code){let e=t.entryLevels[this.selectedEntryIndex];if(e+=s.keyCode===n.Up.code?-1:1,e<0||this.timelineLevels&&e>=this.timelineLevels.length)return this.deselectAllEntries(),s.consume(!0),!0;const o=t.entryStartTimes[this.selectedEntryIndex]+t.entryTotalTimes[this.selectedEntryIndex]/2,a=this.timelineLevels?this.timelineLevels[e]:[];let l=r.ArrayUtilities.upperBound(a,o,(function(e,i){if(!t)throw new Error("No timeline data");return e-t.entryStartTimes[i]}))-1;return!i(this.selectedEntryIndex,a[l])&&(++l,l>=a.length||!i(this.selectedEntryIndex,a[l]))?"ArrowDown"!==s.code&&(this.deselectAllEntries(),s.consume(!0),!0):(s.consume(!0),this.dispatchEventToListeners("EntrySelected",a[l]),!0)}return"Enter"===e.key&&(e.consume(!0),this.dispatchEventToListeners("EntryInvoked",this.selectedEntryIndex),!0)}coordinatesToEntryIndex(e,t){if(e<0||t<0)return-1;const i=this.timelineData();if(!i)return-1;if(t+=this.chartViewport.scrollOffset(),!this.visibleLevelOffsets||!this.visibleLevelHeights||!this.visibleLevels)throw new Error("No visible level offsets or heights");let s=-1;for(let e=0;e<this.dataProvider.maxStackDepth();e++)if(t>=this.visibleLevelOffsets[e]&&t<this.visibleLevelOffsets[e]+(this.visibleLevels[e]?this.visibleLevelHeights[e]:0)){s=e;break}if(s<0||!this.visibleLevels[s])return-1;if(t-this.visibleLevelOffsets[s]>this.levelHeight(s))return-1;for(const[t,r]of this.markerPositions)if(i.entryLevels[t]===s&&r.x<=e&&e<r.x+r.width)return t;const n=i.entryStartTimes,o=this.timelineLevels?this.timelineLevels[s]:[];if(!o||!o.length)return-1;const a=this.chartViewport.pixelToTime(e),l=Math.max(r.ArrayUtilities.upperBound(o,a,((e,t)=>e-n[t]))-1,0);function h(t){if(void 0===t)return!1;if(!i)return!1;const s=n[t],r=i.entryTotalTimes[t],o=this.chartViewport.timeToPosition(s),a=this.chartViewport.timeToPosition(s+r);return o-this.hitMarginPx<e&&e<a+this.hitMarginPx}let d=o[l];return h.call(this,d)?d:(d=o[l+1],h.call(this,d)?d:-1)}isMouseOverRevealChildrenArrow(e,t){if(!this.entryHasDecoration(t,"HIDDEN_DESCENDANTS_ARROW"))return!1;const i=this.timelineData();if(!i)return!1;const s=i.entryStartTimes[t],r=i.entryTotalTimes[t],n=this.chartViewport.timeToPosition(s+r);return n-this.#ne(i,t)-this.hitMarginPx<e&&e<n+this.hitMarginPx}entryIndexToCoordinates(e){const t=this.timelineData(),{x:i,y:s}=this.canvas.getBoundingClientRect();if(!t||!this.visibleLevelOffsets)return null;return{x:this.chartViewport.timeToPosition(t.entryStartTimes[e])+i,y:this.visibleLevelOffsets[t.entryLevels[e]]-this.chartViewport.scrollOffset()+s}}entryTitle(e){return this.dataProvider.entryTitle(e)}getCanvasOffset(){return this.canvas.getBoundingClientRect()}getCanvas(){return this.canvas}getScrollOffset(){return this.chartViewport.scrollOffset()}getContextMenu(){return this.contextMenu}coordinatesToGroupIndex(e,t,i){if(!this.rawTimelineData||!this.rawTimelineData.groups||!this.groupOffsets)return-1;if(e<0||t<0)return-1;t+=this.chartViewport.scrollOffset();const s=this.rawTimelineData.groups||[];let r=-1;if(this.#J){const a=[];function l(e){a.push(e.index);for(const t of e.children)l(t)}if(l(this.#J),a.shift(),a.length!==s.length)return console.warn("The data from the group tree doesn't match the data from DataProvider."),-1;a.push(s.length);for(let h=0;h<a.length;h++){const d=a[h],c=a[h+1]??a.length;if(t>=this.groupOffsets[d]&&t<this.groupOffsets[c]){(!i||t<this.groupOffsets[d]+s[d].style.height)&&(r=d);break}}}if(r<0||r>=s.length)return-1;if(!i)return r;const n=this.canvas.getContext("2d");n.save(),n.font=this.#Q;const o=this.headerLeftPadding+this.labelWidthForGroup(n,s[r]);return n.restore(),e>o?-1:r}markerIndexBeforeTime(e){const t=this.timelineData();if(!t)throw new Error("No timeline data");if(!t.markers)throw new Error("No timeline markers");return r.ArrayUtilities.lowerBound(t.markers,e,((e,t)=>e-t.startTime()))}draw(){const e=this.timelineData();if(!e)return;const t=this.offsetWidth,s=this.offsetHeight,r=this.canvas.getContext("2d");r.save();const n=window.devicePixelRatio,o=this.chartViewport.scrollOffset();r.scale(n,n),r.fillStyle="rgba(0, 0, 0, 0)",r.fillRect(0,0,t,s),r.translate(0,-o),r.font=this.#Q;const{markerIndices:a,colorBuckets:h,titleIndices:d}=this.getDrawableData(r,e);r.save(),this.forEachGroupInViewport(((e,s,n,o,a)=>{this.isGroupFocused(s)&&(r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-background",this.contentElement),r.fillRect(0,e,t,a-n.style.padding))})),r.restore();const c=this.rawTimelineData?.groups||[],u=c.findIndex((e=>e.name.includes("Main"))),m=c.at(u),g=m?.startLevel,p=c.at(u+1)?.startLevel,f=i=>{if(u<0||void 0===g||void 0===p)return!1;const s=Math.min(this.#se(e,i),t);return e.entryLevels[i]>=g&&e.entryLevels[i]<p&&s>10};let v=!1;for(const[t,{indexes:i}]of h)v||(v=i.some(f)),this.#re(r,e,t,i);this.dispatchEventToListeners("ChartPlayableStateChange",v);const w=Array.from(h.values()).map((e=>e.indexes)).flat();this.#oe(r,e,w),this.drawMarkers(r,e,a),this.drawEventTitles(r,e,d,t),r.restore(),this.drawGroupHeaders(t,s),this.drawFlowEvents(r,e),this.drawMarkerLines();const b=M.calculateGridOffsets(this),y=this.dataProvider.mainFrameNavigationStartEvents?.()||[];let T=0;const x=e=>{if(0===y.length)return this.formatValue(e,b.precision);if(y.length>T+1){const t=y[T+1];e>l.Helpers.Timing.microSecondsToMilliseconds(t.ts)&&T++}const t=y[T];if(t){e-=l.Helpers.Timing.microSecondsToMilliseconds(t.ts)-this.zeroTime()}return this.formatValue(e,b.precision)};M.drawCanvasGrid(r,b),this.rulerEnabled&&M.drawCanvasHeaders(r,b,x,3,z),this.updateElementPosition(this.highlightElement,this.highlightedEntryIndex),this.updateElementPosition(this.selectedElement,this.selectedEntryIndex),-1!==this.#ee&&this.showPopoverForSearchResult(this.#ee);for(const e of this.#te)this.updateElementPosition(e,Number(e.id));this.updateMarkerHighlight()}#re(e,t,i,s){e.save(),e.beginPath();for(let i=0;i<s.length;++i){const r=s[i];this.#ae(e,t,r)}e.fillStyle=i,e.fill(),e.restore()}#oe(e,t,i){const{entryTotalTimes:s,entryStartTimes:r,entryLevels:n}=t;e.save();for(let o=0;o<i.length;++o){const a=i[o],h=t.entryDecorations.at(a);if(!h||h.length<1)continue;h.length>1&&F(h);const d=r[a],c=s[a],u=this.timeToPositionClipped(d),m=n[a],g=this.#ne(t,a),p=this.levelToOffset(m);let f=this.#se(t,a);for(const i of h)switch(i.type){case"CANDY":{const s=l.Helpers.Timing.microSecondsToMilliseconds(i.startAtTime);if(c<s)continue;this.candyStripePattern||(this.candyStripePattern=this.createCandyStripePattern()),e.save(),e.beginPath();const r=this.timeToPositionClipped(d+s),n=i.endAtTime?l.Helpers.Timing.microSecondsToMilliseconds(i.endAtTime):d+c,o=this.timeToPositionClipped(n);this.#ae(e,t,a,{startX:r,width:o-r}),e.fillStyle=this.candyStripePattern,e.fill(),e.restore();break}case"WARNING_TRIANGLE":{if(void 0!==i.customEndTime){const e=l.Helpers.Timing.microSecondsToMilliseconds(i.customEndTime);f=this.timeToPositionClipped(e)-u}const t=8;e.save(),e.beginPath(),e.rect(u,p,f,g),e.clip(),e.beginPath(),e.fillStyle="red",e.moveTo(u+f-t,p),e.lineTo(u+f,p),e.lineTo(u+f,p+t),e.fill(),e.restore();break}case"HIDDEN_DESCENDANTS_ARROW":e.save(),e.beginPath(),e.rect(u,p,f,g);if(f>2*g){const t=7,i=5,s=6;e.clip(),e.beginPath(),e.fillStyle="#474747";const r=u+f-t-i,n=p+s;e.moveTo(r,n);const o=u+f-i,a=p+s;e.lineTo(o,a);const l=u+f-i-t/2,h=p+g-s;e.lineTo(l,h)}else{const t=8;e.clip(),e.beginPath(),e.fillStyle="#474747",e.moveTo(u+f-t,p+g),e.lineTo(u+f,p+g),e.lineTo(u+f,p+t)}e.fill(),e.restore();break}}e.restore()}#ae(e,t,i,s){const{entryTotalTimes:r,entryStartTimes:n,entryLevels:o}=t,a=r[i];if(isNaN(a))return;const l=n[i],h=s?.startX??this.timeToPositionClipped(l),d=o[i],c=this.#ne(t,i),u=this.levelToOffset(d),m=s?.width??this.#se(t,i);0!==m&&e.rect(h,u,m-.5,c-1)}#ne(e,t){const{entryLevels:i}=e,s=i[t];return this.levelHeight(s)}entryWidth(e){const t=this.timelineData();return t?this.#se(t,e):0}#se(e,t){const{entryTotalTimes:i,entryStartTimes:s}=e,r=i[t],n=s[t],o=this.timeToPositionClipped(n),a=this.timeToPositionClipped(n+r);return Math.max(a-o,1)}getDrawableData(e,t){const i=[],s=[],{entryTotalTimes:n,entryStartTimes:a}=t,l=this.offsetHeight,h=this.chartViewport.scrollOffset(),d=this.visibleLevelOffsets??new Uint32Array,c=2*this.textPadding+o.UIUtils.measureTextWidth(e,"…"),u=this.chartViewport.pixelToTimeOffset(c),m=Math.max(r.ArrayUtilities.upperBound(d,h,r.ArrayUtilities.DEFAULT_COMPARATOR)-1,0),g=new Map;for(let e=m;e<this.dataProvider.maxStackDepth()&&!(this.levelToOffset(e)>h+l);++e){if(!this.visibleLevels||!this.visibleLevels[e])continue;if(!this.timelineLevels)continue;const t=this.timelineLevels[e];let o=1/0;for(let e=r.ArrayUtilities.lowerBound(t,this.chartViewport.windowRightTime(),((e,t)=>e-a[t]))-1;e>=0;--e){const r=t[e],l=n[r];if(isNaN(l)){s.push(r);continue}(l>=u||this.forceDecorationCache&&this.forceDecorationCache[r])&&i.push(r);const h=a[r];if(h+l<=this.chartViewport.windowLeftTime())break;const d=this.timeToPositionClipped(h);if(!(d>=o)&&(o=d,this.entryColorsCache)){const e=this.entryColorsCache[r];let t=g.get(e);t||(t={indexes:[]},g.set(e,t)),t.indexes.push(r)}}}return{colorBuckets:g,titleIndices:i,markerIndices:s}}drawGroupHeaders(e,t){const r=this.canvas.getContext("2d"),n=this.chartViewport.scrollOffset(),o=window.devicePixelRatio;if(!this.rawTimelineData)return;const a=this.rawTimelineData.groups||[];if(!a.length)return;const l=this.groupOffsets;if(null==l)return;const h=l[l.length-1];function d(t){r.moveTo(0,t),r.lineTo(e,t)}function c(e,t,i){const s=this.arrowSide*Math.sqrt(3)/2,n=Math.round(s/2);r.save(),r.beginPath(),r.translate(e,t),r.rotate(i?Math.PI/2:0),r.moveTo(-n,-this.arrowSide/2),r.lineTo(-n,this.arrowSide/2),r.lineTo(s-n,0),r.fill(),r.restore()}r.save(),r.scale(o,o),r.translate(0,-n),r.font=this.#Q,r.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"),this.forEachGroupInViewport(((t,i,s)=>{const n=s.style.padding;n<5||r.fillRect(0,t-n+2,e,n-4)})),a.length&&h<n+t&&r.fillRect(0,h+2,e,n+t-h),r.strokeStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-container"),r.beginPath(),this.forEachGroupInViewport(((e,t,i,s)=>{s||i.style.padding<4||d(e-2.5)})),d(h+1.5),r.stroke(),this.forEachGroupInViewport(((t,i,s)=>{if(s.style.useFirstLineForOverview)return;if(!this.isGroupCollapsible(i)||s.expanded)return void(!s.style.shareHeaderLine&&this.isGroupFocused(i)&&(r.fillStyle=s.style.backgroundColor,r.fillRect(0,t,e,s.style.height)));let n=i+1;for(;n<a.length&&a[n].style.nestingLevel>s.style.nestingLevel;)n++;const o=n<a.length?a[n].startLevel:this.dataProvider.maxStackDepth();this.drawCollapsedOverviewForGroup(s,t,o)})),r.save(),this.forEachGroupInViewport(((e,t,n)=>{if(r.font=this.#Q,this.isGroupCollapsible(t)&&!n.expanded||n.style.shareHeaderLine){const o=this.labelWidthForGroup(r,n)+2;if(this.isGroupFocused(t))r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-background",this.contentElement);else{const e=s.Color.parse(n.style.backgroundColor);e&&(r.fillStyle=e.setAlpha(.8).asString())}r.fillRect(this.headerLeftPadding-this.headerLabelXPadding,e+this.headerLabelYPadding,o,n.style.height-2*this.headerLabelYPadding)}r.fillStyle=n.style.color,r.fillText(n.name,Math.floor(this.expansionArrowIndent*(n.style.nestingLevel+1)+this.arrowSide),e+n.style.height-this.textBaseline)})),r.restore(),r.fillStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-token-subtle"),this.forEachGroupInViewport(((e,t,i)=>{this.isGroupCollapsible(t)&&c.call(this,this.expansionArrowIndent*(i.style.nestingLevel+1),e+i.style.height-this.textBaseline-this.arrowSide/2,Boolean(i.expanded))})),r.strokeStyle=i.ThemeSupport.instance().getComputedValue("--sys-color-neutral-outline"),r.beginPath(),r.stroke(),this.forEachGroupInViewport(((e,t,s,n,o)=>{if(this.isGroupFocused(t)){const t=2,n=10;r.fillStyle=i.ThemeSupport.instance().getComputedValue("--selected-group-border",this.contentElement),r.fillRect(0,e-t,t,o-s.style.padding+2*t),r.fillRect(0,e-t,n,t),r.fillRect(0,e+o-s.style.padding,n,t)}})),r.restore()}drawMarkers(e,t,i){const{entryStartTimes:s,entryLevels:r}=t;this.markerPositions.clear(),e.textBaseline="alphabetic",e.save(),e.beginPath();let n=-1,a=-1/0;for(let t=i.length-1;t>=0;--t){const l=i[t],h=this.dataProvider.entryTitle(l);if(!h)continue;const d=s[l],c=r[l];n!==c&&(a=-1/0);const u=Math.max(this.chartViewport.timeToPosition(d),a),m=this.levelToOffset(c),g=this.levelHeight(c),p=4,f=Math.ceil(o.UIUtils.measureTextWidth(e,h))+2*p;a=u+f+1,n=c,this.markerPositions.set(l,{x:u,width:f}),e.fillStyle=this.dataProvider.entryColor(l),e.fillRect(u,m,f,g-1),e.fillStyle="white",e.fillText(h,u+p,m+g-this.textBaseline)}e.strokeStyle="rgba(0, 0, 0, 0.2)",e.stroke(),e.restore()}drawEventTitles(e,t,i,s){const r=this.chartViewport.timeToPixel(),n=this.textPadding;e.save(),e.beginPath();const{entryStartTimes:a,entryLevels:l}=t;for(let h=0;h<i.length;++h){const d=i[h],c=a[d],u=this.timeToPositionClipped(c),m=Math.min(this.#se(t,d),s),g=l[d],p=this.levelToOffset(g);let f=this.dataProvider.entryTitle(d);const v=this.#ne(t,d);if(f&&f.length){e.font=this.#Q;const t=this.entryHasDecoration(d,"HIDDEN_DESCENDANTS_ARROW")&&m>2*v?m-n-this.barHeight:m-2*n;f=o.UIUtils.trimTextMiddle(e,f,t)}const w=this.chartViewport.timeToPosition(c);this.dataProvider.decorateEntry(d,e,f,u,p,m,v,w,r)||f&&f.length&&(e.fillStyle=this.dataProvider.textColor(d),e.fillText(f,u+n,p+v-this.textBaseline))}e.restore()}forEachGroup(e){if(!this.rawTimelineData)return;const t=this.rawTimelineData.groups||[];if(!t.length)return;const i=this.groupOffsets;if(!i)return;const s=[{nestingLevel:-1,visible:!0}];for(let r=0;r<t.length;++r){const n=i[r],o=t[r];let a=!0,l=s[s.length-1];for(;l&&l.nestingLevel>=o.style.nestingLevel;)s.pop(),a=!1,l=s[s.length-1];l=s[s.length-1];const h=!!l&&l.visible,d=!o.hidden&&h&&(!this.isGroupCollapsible(r)||o.expanded);s.push({nestingLevel:o.style.nestingLevel,visible:Boolean(d)});const c=r===t.length-1?i[r+1]+o.style.padding:i[r+1];h&&!o.hidden&&e(n,r,o,a,c-n)}}forEachGroupInViewport(e){const t=this.chartViewport.scrollOffset();this.forEachGroup(((i,s,r,n,o)=>{i-r.style.padding>t+this.offsetHeight||i+o<t||e(i,s,r,n,o)}))}labelWidthForGroup(e,t){return o.UIUtils.measureTextWidth(e,t.name)+this.expansionArrowIndent*(t.style.nestingLevel+1)+2*this.headerLabelXPadding}drawCollapsedOverviewForGroup(e,t,i){const n=new s.SegmentedRange.SegmentedRange((function(e,t){return e.data===t.data&&e.end+.4>t.end?e:null})),o=this.chartViewport.windowLeftTime(),a=this.chartViewport.windowRightTime(),l=this.canvas.getContext("2d"),h=e.style.height;if(!this.rawTimelineData)return;const d=this.rawTimelineData.entryStartTimes,c=this.rawTimelineData.entryTotalTimes,u=this.chartViewport.timeToPixel();for(let m=e.startLevel;m<i;++m){const i=this.timelineLevels?this.timelineLevels[m]:[];let g=1/0;for(let m=r.ArrayUtilities.lowerBound(i,a,((e,t)=>e-d[t]))-1;m>=0;--m){const r=i[m],a=d[r],p=this.timeToPositionClipped(a),f=a+c[r];if(isNaN(f)||p>=g)continue;if(f<=o)break;g=p;const v=this.entryColorsCache?this.entryColorsCache[r]:"",w=this.timeToPositionClipped(f);if(e.style.useDecoratorsForOverview&&this.dataProvider.forceDecoration(r)){const e=this.chartViewport.timeToPosition(a),i=this.#se(this.rawTimelineData,r);l.beginPath(),l.fillStyle=v,l.fillRect(p,t,i,h-1),this.dataProvider.decorateEntry(r,l,"",p,t,i,h,e,u)}else n.append(new s.SegmentedRange.Segment(p,w,v))}}const m=n.segments().slice().sort(((e,t)=>e.data.localeCompare(t.data)));let g;l.beginPath();for(let e=0;e<m.length;++e){const i=m[e];g!==m[e].data&&(l.fill(),l.beginPath(),g=m[e].data,l.fillStyle=g),l.rect(i.begin,t,i.end-i.begin,h)}l.fill()}drawFlowEvents(e,t){const i=this.timelineData();if(!i)return;const{entryTotalTimes:s,entryStartTimes:r,entryLevels:n}=t,o=window.devicePixelRatio,a=this.chartViewport.scrollOffset();e.save(),e.scale(o,o),e.translate(0,-a),e.fillStyle="#7f5050",e.strokeStyle="#7f5050";for(let o=0;o<i.initiatorsData.length;++o){const a=i.initiatorsData[o],l=r[a.initiatorIndex]+s[a.initiatorIndex],h=r[a.eventIndex];if(h<this.chartViewport.windowLeftTime())continue;let d=this.chartViewport.timeToPosition(l),c=this.chartViewport.timeToPosition(h);if(a.isInitiatorHidden){const{circleEndX:i}=this.drawCircleArroundCollapseArrow(a.initiatorIndex,e,t);i&&(d=i)}if(a.isEntryHidden){const{circleStartX:i}=this.drawCircleArroundCollapseArrow(a.eventIndex,e,t);i&&(c=i)}const u=n[a.initiatorIndex],m=n[a.eventIndex],g=this.levelToOffset(u)+this.levelHeight(u)/2,p=this.levelToOffset(m)+this.levelHeight(m)/2,f=c-d;e.beginPath(),e.moveTo(d,g),e.lineTo(d+f/2,g),e.lineTo(d+f/2,p),e.lineTo(c,p),e.stroke(),f>3?(e.lineWidth=.5,e.beginPath(),e.moveTo(c,p),e.lineTo(c-6,p-3),e.lineTo(c-6,p+3),e.fill()):e.lineWidth=.2}e.restore()}drawCircleArroundCollapseArrow(e,t,i){const s=i.entryDecorations.at(e);if(!s||!s.find((e=>"HIDDEN_DESCENDANTS_ARROW"===e.type)))return{};const{entryStartTimes:r,entryLevels:n}=i,o=this.#se(i,e);if(o<2*this.barHeight)return{};const a=r[e],l=this.timeToPositionClipped(a),h=n[e],d=this.#ne(i,e),c=this.levelToOffset(h);t.save(),t.beginPath(),t.rect(l,c,o,d),t.clip(),t.lineWidth=1,t.beginPath(),t.fillStyle="#474747";const u=l+o-this.barHeight/2,m=c+this.barHeight/2;return t.beginPath(),t.arc(u,m,6,0,2*Math.PI),t.stroke(),t.restore(),{circleStartX:u-6,circleEndX:u+6}}drawMarkerLines(){const e=this.timelineData();if(!e)return;const t=e.markers,i=this.markerIndexBeforeTime(this.minimumBoundary()),s=this.maximumBoundary(),r=this.chartViewport.timeToPixel(),n=this.canvas.getContext("2d");n.save();const o=window.devicePixelRatio;n.scale(o,o),n.translate(0,3);const a=z-1;for(let e=i;e<t.length;e++){const i=t[e].startTime();if(i>s)break;t[e].draw(n,this.chartViewport.timeToPosition(i),a,r)}n.restore()}updateMarkerHighlight(){const e=this.markerHighlighElement;e.parentElement&&e.remove();const t=this.highlightedMarkerIndex;if(-1===t)return;const i=this.timelineData();if(!i)return;const s=i.markers[t],r=this.timeToPositionClipped(s.startTime());o.Tooltip.Tooltip.install(e,s.title()||"");const n=e.style;n.left=r+"px",n.backgroundColor=s.color(),this.viewportElement.appendChild(e)}processTimelineData(e){if(!e)return this.timelineLevels=null,this.visibleLevelOffsets=null,this.visibleLevels=null,this.groupOffsets=null,this.rawTimelineData=null,this.forceDecorationCache=null,this.entryColorsCache=null,this.rawTimelineDataLength=0,this.#J=null,this.selectedGroupIndex=-1,this.keyboardFocusedGroup=-1,void this.flameChartDelegate.updateSelectedGroup(this,null);this.rawTimelineData=e,this.rawTimelineDataLength=e.entryStartTimes.length,this.forceDecorationCache=new Int8Array(this.rawTimelineDataLength),this.entryColorsCache=new Array(this.rawTimelineDataLength);for(let e=0;e<this.rawTimelineDataLength;++e)this.forceDecorationCache[e]=this.dataProvider.forceDecoration(e)?1:0,this.entryColorsCache[e]=this.dataProvider.entryColor(e);const t=new Uint32Array(this.dataProvider.maxStackDepth()+1);for(let i=0;i<e.entryLevels.length;++i)++t[e.entryLevels[i]];const i=new Array(t.length);for(let e=0;e<i.length;++e)i[e]=new Uint32Array(t[e]),t[e]=0;for(let s=0;s<e.entryLevels.length;++s){const r=e.entryLevels[s];i[r][t[r]++]=s}this.timelineLevels=i;const s=this.rawTimelineData.groups||[];for(let e=0;e<s.length;++e){const t=this.groupExpansionState[s[e].name],i=this.groupHiddenState[s[e].name];void 0!==t&&(s[e].expanded=t),void 0!==i&&(s[e].hidden=i)}this.#J?this.updateGroupTree(s,this.#J):this.#J=this.buildGroupTree(s),this.updateLevelPositions(),this.updateHeight(),-1===this.selectedGroupIndex&&(this.selectedGroupIndex=e.selectedGroup?s.indexOf(e.selectedGroup):-1),this.keyboardFocusedGroup=this.selectedGroupIndex,this.flameChartDelegate.updateSelectedGroup(this,e.selectedGroup)}#le(e,t,i){return{index:t,nestingLevel:e.style.nestingLevel,startLevel:e.startLevel,endLevel:i,children:[]}}buildGroupTree(e){const t={index:-1,nestingLevel:-1,startLevel:0,endLevel:e.length?e[0].startLevel:this.dataProvider.maxStackDepth(),children:[]},i=[t];for(let t=0;t<e.length;t++){const s=e[t],r=s.style.nestingLevel;let n=i[i.length-1];for(;n&&n.nestingLevel>=r;)i.pop(),n=i[i.length-1];const o=e[t+1],a=o?.startLevel??this.dataProvider.maxStackDepth(),l=this.#le(s,t,a);n.children.push(l),i.push(l)}return t}updateGroupTree(e,t){const i=this.dataProvider.maxStackDepth();!function t(s){const r=s.index;if(r<0)s.startLevel=0,s.endLevel=e.length?e[0].startLevel:i;else{if(!e[r])return void console.warn("The |groups| is changed. Please make sure the flamechart is reset after data change in the data provider");s.startLevel=e[r].startLevel;const t=e[r+1];s.endLevel=t?.startLevel??i}for(const e of s.children)t(e)}(t)}#he(e,t,i){if(!(this.visibleLevels&&this.visibleLevelOffsets&&this.visibleLevelHeights&&this.groupOffsets))return t;const s=this.rawTimelineData?.groups;if(!s)return t;if(e.index>=s.length)return console.warn("The data from the group tree is outdated. Please make sure the flamechart is reset after data change in the data provider"),t;e.index>=0&&(this.groupOffsets[e.index]=t,s[e.index].hidden||!i||s[e.index].style.shareHeaderLine||(t+=s[e.index].style.height));let r=!1;if(e.index<0)r=!0;else{const t=!(this.isGroupCollapsible(e.index)&&!s[e.index].expanded);r=!s[e.index].hidden&&t}const n=r&&i;for(let r=e.startLevel;r<e.endLevel;r++){if(r>=this.dataProvider.maxStackDepth())return console.warn("The data from the group tree is outdated. Please make sure the flamechart is reset after data change in the data provider"),t;const o=r===e.startLevel;let a,l;if(e.index<0)a=!0;else{const t=o&&s[e.index].style.useFirstLineForOverview;a=!s[e.index].hidden&&i&&(n||t)}if(s[e.index]){const t=o&&!s[e.index].style.shareHeaderLine,i=this.isGroupCollapsible(e.index)&&!s[e.index].expanded;l=t||i?s[e.index].style.height:s[e.index].style.itemsHeight??this.barHeight}else l=this.barHeight;this.visibleLevels[r]=a??!1,this.visibleLevelOffsets[r]=t,this.visibleLevelHeights[r]=l,(a||i&&s[e.index].style.shareHeaderLine&&o)&&(t+=l)}if(0===e.children.length)return t;for(const i of e.children)n&&!s[i.index]?.hidden&&i!==e.children[0]&&(t+=s[i.index].style.padding??0),t=this.#he(i,t,n);return t}updateLevelPositions(){if(!this.#J)return void console.warn("Please make sure the new timeline data is processed before update the level positions.");const e=this.dataProvider.maxStackDepth(),t=this.rawTimelineData?.groups||[];this.visibleLevelOffsets=new Uint32Array(e+1),this.visibleLevelHeights=new Uint32Array(e),this.visibleLevels=new Array(e),this.groupOffsets=new Uint32Array(t.length+1);let i=this.rulerEnabled?z+2:2;i=this.#he(this.#J,i,!0),this.groupOffsets[t.length]=i,this.visibleLevelOffsets[e]=i}isGroupCollapsible(e){if(!this.rawTimelineData||e<0)return;const t=this.rawTimelineData.groups||[],i=t[e].style;if(!i.shareHeaderLine||!i.collapsible)return Boolean(i.collapsible);const s=e+1>=t.length;if(!s&&t[e+1].style.nestingLevel>i.nestingLevel)return!0;return(s?this.dataProvider.maxStackDepth():t[e+1].startLevel)!==t[e].startLevel+1||i.height!==i.itemsHeight}setSelectedEntry(e){this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e)&&this.modifyTree("RESET_CHILDREN",e),this.selectedEntryIndex!==e&&(-1!==e&&this.chartViewport.hideRangeSelection(),this.selectedEntryIndex=e,this.revealEntry(e),this.updateElementPosition(this.selectedElement,this.selectedEntryIndex))}entryHasDecoration(e,t){const i=this.timelineData();if(!i)return!1;const s=i.entryDecorations.at(e);return!!(s&&s.length>=1)&&s.some((e=>e.type===t))}updateElementPosition(e,t,i){if(e.classList.add("hidden"),-1===t)return;const s=this.timelineData();if(!s)return;const r=s.entryStartTimes[t],n=s.entryTotalTimes[t];let o=0,a=0,l=!0;if(Number.isNaN(n)){const e=this.markerPositions.get(t);e?(o=e.x,a=e.width):l=!1}else o=this.chartViewport.timeToPosition(r),a=n*this.chartViewport.timeToPixel();if(o+a<=0||o>=this.offsetWidth)return;const h=o+a/2;a=Math.max(a,2),o=h-a/2;const d=s.entryLevels[t],c=this.levelToOffset(d)-this.chartViewport.scrollOffset(),u=this.levelHeight(d),m=e.style;i?(m.top=c+"px",m.width=u+"px",m.height=u+"px",m.left=o+a-u+"px"):(m.top=c+"px",m.width=a+"px",m.height=u-1+"px",m.left=o+"px"),e.classList.toggle("hidden",!l),this.viewportElement.appendChild(e)}updateHiddenChildrenArrowHighlighPosition(e){this.revealDescendantsArrowHighlightElement.classList.add("hidden"),-1!==e&&this.isMouseOverRevealChildrenArrow(this.lastMouseOffsetX,e)&&this.updateElementPosition(this.revealDescendantsArrowHighlightElement,e,!0)}timeToPositionClipped(e){return r.NumberUtilities.clamp(this.chartViewport.timeToPosition(e),0,this.offsetWidth)}groupIndexToOffsetForTest(e){if(!this.groupOffsets)throw new Error("No visible group offsets");return this.groupOffsets[e]}levelVisibilityForTest(e){if(!this.visibleLevels)throw new Error("No level visiblibities");return this.visibleLevels[e]}levelToOffset(e){if(!this.visibleLevelOffsets)throw new Error("No visible level offsets");return this.visibleLevelOffsets[e]}levelHeight(e){if(!this.visibleLevelHeights)throw new Error("No visible level heights");return this.visibleLevelHeights[e]}updateBoundaries(){this.totalTime=this.dataProvider.totalTime(),this.minimumBoundaryInternal=this.dataProvider.minimumBoundary(),this.chartViewport.setBoundaries(this.minimumBoundaryInternal,this.totalTime)}updateHeight(){const e=this.levelToOffset(this.dataProvider.maxStackDepth())+2;this.chartViewport.setContentHeight(e)}onResize(){this.scheduleUpdate()}update(){this.timelineData()&&(this.resetCanvas(),this.updateHeight(),this.updateBoundaries(),this.draw(),this.chartViewport.isDragging()||this.updateHighlight())}reset(){this.chartViewport.reset(),this.rawTimelineData=null,this.rawTimelineDataLength=0,this.#J=null,this.highlightedMarkerIndex=-1,this.highlightedEntryIndex=-1,this.selectedEntryIndex=-1,this.selectedGroupIndex=-1}scheduleUpdate(){this.chartViewport.scheduleUpdate()}enabled(){return 0!==this.rawTimelineDataLength}computePosition(e){return this.chartViewport.timeToPosition(e)}formatValue(e,t){return this.dataProvider.formatValue(e-this.zeroTime(),t)}maximumBoundary(){return l.Types.Timing.MilliSeconds(this.chartViewport.windowRightTime())}minimumBoundary(){return l.Types.Timing.MilliSeconds(this.chartViewport.windowLeftTime())}zeroTime(){return l.Types.Timing.MilliSeconds(this.dataProvider.minimumBoundary())}boundarySpan(){return l.Types.Timing.MilliSeconds(this.maximumBoundary()-this.minimumBoundary())}}const z=15,W={CANDY:1,WARNING_TRIANGLE:2,HIDDEN_DESCENDANTS_ARROW:3};function F(e){e.sort(((e,t)=>W[e.type]-W[t.type]))}class V{entryLevels;entryTotalTimes;entryStartTimes;entryDecorations;groups;markers;initiatorsData;selectedGroup;constructor(e,t,i,s,r=[],n=[]){this.entryLevels=e,this.entryTotalTimes=t,this.entryStartTimes=i,this.entryDecorations=r,this.groups=s||[],this.markers=[],this.initiatorsData=n||[],this.selectedGroup=null}static create(e){return new V(e.entryLevels,e.entryTotalTimes,e.entryStartTimes,e.groups,e.entryDecorations||[],e.initiatorsData||[])}static createEmpty(){return new V([],[],[],[])}resetFlowData(){this.initiatorsData=[]}}var U=Object.freeze({__proto__:null,FlameChartDelegate:class{windowChanged(e,t,i){}updateRangeSelection(e,t){}updateSelectedGroup(e,t){}},FlameChart:G,RulerHeight:z,MinimalTimeWindowMs:.5,sortDecorationsForRenderingOrder:F,FlameChartTimelineData:V});const N=n.RenderCoordinator.RenderCoordinator.instance();class _ extends o.Widget.VBox{delegate;viewportElement;alwaysShowVerticalScrollInternal;rangeSelectionEnabled;vScrollElement;vScrollContent;selectionOverlay;selectedTimeSpanLabel;cursorElement;isDraggingInternal;totalHeight;offsetHeight;scrollTop;rangeSelectionStart;rangeSelectionEnd;dragStartPointX;dragStartPointY;dragStartScrollTop;visibleLeftTime;visibleRightTime;offsetWidth;targetLeftTime;targetRightTime;selectionOffsetShiftX;selectionStartX;lastMouseOffsetX;minimumBoundary;totalTime;isUpdateScheduled;cancelWindowTimesAnimation;constructor(e){super(),this.registerRequiredCSS(S),this.delegate=e,this.viewportElement=this.contentElement.createChild("div","fill"),this.viewportElement.addEventListener("mousemove",this.updateCursorPosition.bind(this),!1),this.viewportElement.addEventListener("mouseout",this.onMouseOut.bind(this),!1),this.viewportElement.addEventListener("wheel",this.onMouseWheel.bind(this),!1),this.viewportElement.addEventListener("keydown",this.onChartKeyDown.bind(this),!1),this.viewportElement.addEventListener("keyup",this.onChartKeyUp.bind(this),!1),o.UIUtils.installDragHandle(this.viewportElement,this.startDragging.bind(this),this.dragging.bind(this),this.endDragging.bind(this),"-webkit-grabbing",null),o.UIUtils.installDragHandle(this.viewportElement,this.startRangeSelection.bind(this),this.rangeSelectionDragging.bind(this),this.endRangeSelection.bind(this),"text",null),this.alwaysShowVerticalScrollInternal=!1,this.rangeSelectionEnabled=!0,this.vScrollElement=this.contentElement.createChild("div","chart-viewport-v-scroll"),this.vScrollContent=this.vScrollElement.createChild("div"),this.vScrollElement.addEventListener("scroll",this.onScroll.bind(this),!1),this.selectionOverlay=this.contentElement.createChild("div","chart-viewport-selection-overlay hidden"),this.selectedTimeSpanLabel=this.selectionOverlay.createChild("div","time-span"),this.cursorElement=this.contentElement.createChild("div","chart-cursor-element hidden"),this.reset(),this.rangeSelectionStart=null,this.rangeSelectionEnd=null}alwaysShowVerticalScroll(){this.alwaysShowVerticalScrollInternal=!0,this.vScrollElement.classList.add("always-show-scrollbar")}disableRangeSelection(){this.rangeSelectionEnabled=!1,this.rangeSelectionStart=null,this.rangeSelectionEnd=null,this.updateRangeSelectionOverlay()}isDragging(){return this.isDraggingInternal}elementsToRestoreScrollPositionsFor(){return[this.vScrollElement]}updateScrollBar(){const e=this.alwaysShowVerticalScrollInternal||this.totalHeight>this.offsetHeight;this.vScrollElement.classList.contains("hidden")===e&&(this.vScrollElement.classList.toggle("hidden",!e),this.updateContentElementSize())}onResize(){this.updateScrollBar(),this.updateContentElementSize(),this.scheduleUpdate()}reset(){this.vScrollElement.scrollTop=0,this.scrollTop=0,this.rangeSelectionStart=null,this.rangeSelectionEnd=null,this.isDraggingInternal=!1,this.dragStartPointX=0,this.dragStartPointY=0,this.dragStartScrollTop=0,this.visibleLeftTime=0,this.visibleRightTime=0,this.offsetWidth=0,this.offsetHeight=0,this.totalHeight=0,this.targetLeftTime=0,this.targetRightTime=0,this.isUpdateScheduled=!1,this.updateContentElementSize()}updateContentElementSize(){let e=this.vScrollElement.offsetLeft;e||(e=this.contentElement.offsetWidth),this.offsetWidth=e,this.offsetHeight=this.contentElement.offsetHeight,this.delegate.setSize(this.offsetWidth,this.offsetHeight)}setContentHeight(e){this.totalHeight=e,this.vScrollContent.style.height=e+"px",this.updateScrollBar(),this.updateContentElementSize(),this.scrollTop+this.offsetHeight<=e||(this.scrollTop=Math.max(0,e-this.offsetHeight),this.vScrollElement.scrollTop=this.scrollTop)}setScrollOffset(e,t){t=t||0,this.vScrollElement.scrollTop>e?this.vScrollElement.scrollTop=e:this.vScrollElement.scrollTop<e-this.offsetHeight+t&&(this.vScrollElement.scrollTop=e-this.offsetHeight+t)}scrollOffset(){return this.vScrollElement.scrollTop}chartHeight(){return this.offsetHeight}setBoundaries(e,t){this.minimumBoundary=e,this.totalTime=t}onMouseWheel(e){const t=e,i=t.shiftKey!==("zoom"===s.Settings.Settings.instance().moduleSetting("flamechart-mouse-wheel-action").get()),r=!i&&(t.deltaY||53===Math.abs(t.deltaX)),n=i&&Math.abs(t.deltaX)>Math.abs(t.deltaY);if(r)this.vScrollElement.scrollTop+=(t.deltaY||t.deltaX)/53*this.offsetHeight/8;else if(n)this.handlePanGesture(t.deltaX,!0);else{const e=1/53;this.handleZoomGesture(Math.pow(1.2,(t.deltaY||t.deltaX)*e)-1)}e.consume(!0)}startDragging(e){return!e.shiftKey&&(this.isDraggingInternal=!0,this.dragStartPointX=e.pageX,this.dragStartPointY=e.pageY,this.dragStartScrollTop=this.vScrollElement.scrollTop,this.viewportElement.style.cursor="",!0)}dragging(e){const t=this.dragStartPointX-e.pageX;this.dragStartPointX=e.pageX,this.handlePanGesture(t);const i=this.dragStartPointY-e.pageY;this.vScrollElement.scrollTop=this.dragStartScrollTop+i}endDragging(){this.isDraggingInternal=!1}startRangeSelection(e){if(!e.shiftKey||!this.rangeSelectionEnabled)return!1;this.isDraggingInternal=!0,this.selectionOffsetShiftX=e.offsetX-e.pageX,this.selectionStartX=e.offsetX;const t=this.selectionOverlay.style;return t.left=this.selectionStartX+"px",t.width="1px",this.selectedTimeSpanLabel.textContent="",this.selectionOverlay.classList.remove("hidden"),!0}endRangeSelection(){this.isDraggingInternal=!1,this.selectionStartX=null}hideRangeSelection(){this.selectionOverlay.classList.add("hidden"),this.rangeSelectionStart=null,this.rangeSelectionEnd=null}setRangeSelection(e,t){this.rangeSelectionEnabled&&(this.rangeSelectionStart=Math.min(e,t),this.rangeSelectionEnd=Math.max(e,t),this.updateRangeSelectionOverlay(),this.delegate.updateRangeSelection(this.rangeSelectionStart,this.rangeSelectionEnd))}onClick(e){const t=e,i=this.pixelToTime(t.offsetX);null!==this.rangeSelectionStart&&null!==this.rangeSelectionEnd&&i>=this.rangeSelectionStart&&i<=this.rangeSelectionEnd||this.hideRangeSelection()}rangeSelectionDragging(e){const t=r.NumberUtilities.clamp(e.pageX+this.selectionOffsetShiftX,0,this.offsetWidth),i=this.pixelToTime(this.selectionStartX||0),s=this.pixelToTime(t);this.setRangeSelection(i,s)}updateRangeSelectionOverlay(){const t=this.rangeSelectionStart||0,i=this.rangeSelectionEnd||0,s=100,n=r.NumberUtilities.clamp(this.timeToPosition(t),-100,this.offsetWidth+s),o=r.NumberUtilities.clamp(this.timeToPosition(i),-100,this.offsetWidth+s),a=this.selectionOverlay.style;a.left=n+"px",a.width=o-n+"px";const l=i-t;this.selectedTimeSpanLabel.textContent=e.TimeUtilities.preciseMillisToString(l,2)}onScroll(){this.scrollTop=this.vScrollElement.scrollTop,this.scheduleUpdate()}onMouseOut(){this.lastMouseOffsetX=-1,this.showCursor(!1)}updateCursorPosition(e){const t=e;this.lastMouseOffsetX=t.offsetX;const i=t.shiftKey&&!t.metaKey;this.showCursor(i),i&&(this.cursorElement.style.left=t.offsetX+"px")}pixelToTime(e){return this.pixelToTimeOffset(e)+this.visibleLeftTime}pixelToTimeOffset(e){return e*(this.visibleRightTime-this.visibleLeftTime)/this.offsetWidth}timeToPosition(e){return Math.floor((e-this.visibleLeftTime)/(this.visibleRightTime-this.visibleLeftTime)*this.offsetWidth)}timeToPixel(){return this.offsetWidth/(this.visibleRightTime-this.visibleLeftTime)}showCursor(e){this.cursorElement.classList.toggle("hidden",!e||this.isDraggingInternal)}onChartKeyDown(e){const t=e;this.showCursor(t.shiftKey),this.handleZoomPanKeys(e)}onChartKeyUp(e){const t=e;this.showCursor(t.shiftKey)}handleZoomPanKeys(e){if(!o.KeyboardShortcut.KeyboardShortcut.hasNoModifiers(e))return;const t=e,i=t.shiftKey?.8:.3,s=t.shiftKey?320:160;switch(t.code){case"KeyA":this.handlePanGesture(-s,!0);break;case"KeyD":this.handlePanGesture(s,!0);break;case"KeyW":this.handleZoomGesture(-i);break;case"KeyS":this.handleZoomGesture(i);break;default:return}e.consume(!0)}handleZoomGesture(e){const t={left:this.targetLeftTime,right:this.targetRightTime},i=this.pixelToTime(this.lastMouseOffsetX||0);t.left+=(t.left-i)*e,t.right+=(t.right-i)*e,this.requestWindowTimes(t,!0)}handlePanGesture(e,t){const i={left:this.targetLeftTime,right:this.targetRightTime},s=r.NumberUtilities.clamp(this.pixelToTimeOffset(e),this.minimumBoundary-i.left,this.totalTime+this.minimumBoundary-i.right);i.left+=s,i.right+=s,this.requestWindowTimes(i,Boolean(t))}requestWindowTimes(e,t){const i=this.minimumBoundary+this.totalTime;e.left<this.minimumBoundary?(e.right=Math.min(e.right+this.minimumBoundary-e.left,i),e.left=this.minimumBoundary):e.right>i&&(e.left=Math.max(e.left-e.right+i,this.minimumBoundary),e.right=i),e.right-e.left<.5||this.delegate.windowChanged(e.left,e.right,t)}scheduleUpdate(){this.cancelWindowTimesAnimation||this.isUpdateScheduled||(this.isUpdateScheduled=!0,N.write((()=>{this.isUpdateScheduled=!1,this.update()})))}update(){this.updateRangeSelectionOverlay(),this.delegate.update()}setWindowTimes(e,t,i){if(e!==this.targetLeftTime||t!==this.targetRightTime){if(!i||0===this.visibleLeftTime||this.visibleRightTime===1/0||0===e&&t===1/0||e===1/0&&t===1/0)return this.targetLeftTime=e,this.targetRightTime=t,this.visibleLeftTime=e,this.visibleRightTime=t,void this.scheduleUpdate();this.cancelWindowTimesAnimation&&(this.cancelWindowTimesAnimation(),this.visibleLeftTime=this.targetLeftTime,this.visibleRightTime=this.targetRightTime),this.targetLeftTime=e,this.targetRightTime=t,this.cancelWindowTimesAnimation=o.UIUtils.animateFunction(this.element.window(),function(e,t){this.visibleLeftTime=e,this.visibleRightTime=t,this.update()}.bind(this),[{from:this.visibleLeftTime,to:e},{from:this.visibleRightTime,to:t}],100,(()=>{this.cancelWindowTimesAnimation=null}))}}windowLeftTime(){return this.visibleLeftTime}windowRightTime(){return this.visibleRightTime}}var X=Object.freeze({__proto__:null,ChartViewport:_}),$={cssContent:".film-strip-view{overflow-x:auto;overflow-y:hidden;align-content:flex-start;min-height:81px}.film-strip-view .frame .time{font-size:10px;margin-top:2px}.film-strip-view .label{margin:auto;font-size:18px;color:var(--sys-color-token-subtle)}.film-strip-view .frame{background:none;border:none;display:flex;flex-direction:column;align-items:center;padding:4px;flex:none;cursor:pointer}.film-strip-view .frame .thumbnail{min-width:24px;display:flex;flex-direction:row;align-items:center;pointer-events:none;margin:4px 0 2px;border:2px solid transparent}.film-strip-view .frame:hover .thumbnail,\n.film-strip-view .frame:focus .thumbnail{border-color:var(--sys-color-primary)}.film-strip-view .frame .thumbnail img{height:auto;width:auto;max-width:80px;max-height:50px;pointer-events:none;box-shadow:0 0 3px var(--box-shadow-outline-color);flex:0 0 auto}.film-strip-view .frame:hover .thumbnail img,\n.film-strip-view .frame:focus .thumbnail img{box-shadow:none}"};const j={doubleclickToZoomImageClickTo:"Doubleclick to zoom image. Click to view preceding requests.",screenshotForSSelectToView:"Screenshot for {PH1} - select to view preceding requests.",screenshot:"Screenshot",previousFrame:"Previous frame",nextFrame:"Next frame"},Y=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/FilmStripView.ts",j),K=e.i18n.getLocalizedString.bind(void 0,Y);class Z extends(s.ObjectWrapper.eventMixin(o.Widget.HBox)){statusLabel;zeroTime=l.Types.Timing.MilliSeconds(0);#de=null;constructor(){super(!0),this.registerRequiredCSS($),this.contentElement.classList.add("film-strip-view"),this.statusLabel=this.contentElement.createChild("div","label"),this.reset()}static setImageData(e,t){t&&(e.src=t)}setModel(e){this.#de=e,this.zeroTime=l.Helpers.Timing.microSecondsToMilliseconds(e.zeroTime),this.#de.frames.length?this.update():this.reset()}createFrameElement(t){const i=l.Helpers.Timing.microSecondsToMilliseconds(t.screenshotEvent.ts),s=e.TimeUtilities.millisToString(i-this.zeroTime),r=document.createElement("button");r.classList.add("frame"),o.Tooltip.Tooltip.install(r,K(j.doubleclickToZoomImageClickTo)),r.createChild("div","time").textContent=s,r.tabIndex=0,r.setAttribute("jslog",`${d.preview("film-strip").track({click:!0,dblclick:!0})}`),r.setAttribute("aria-label",K(j.screenshotForSSelectToView,{PH1:s})),o.ARIAUtils.markAsButton(r);const n=r.createChild("div","thumbnail").createChild("img");return n.alt=K(j.screenshot),r.addEventListener("mousedown",this.onMouseEvent.bind(this,"FrameSelected",i),!1),r.addEventListener("mouseenter",this.onMouseEvent.bind(this,"FrameEnter",i),!1),r.addEventListener("mouseout",this.onMouseEvent.bind(this,"FrameExit",i),!1),r.addEventListener("dblclick",this.onDoubleClick.bind(this,t),!1),r.addEventListener("focusin",this.onMouseEvent.bind(this,"FrameEnter",i),!1),r.addEventListener("focusout",this.onMouseEvent.bind(this,"FrameExit",i),!1),Z.setImageData(n,t.screenshotEvent.args.dataUri),r}update(){const e=this.#de?.frames;if(!e||e.length<1)return;const t=e.map((e=>this.createFrameElement(e)));this.contentElement.removeChildren();for(const e of t)this.contentElement.appendChild(e)}onMouseEvent(e,t){this.dispatchEventToListeners(e,t)}onDoubleClick(e){this.#de&&q.fromFilmStrip(this.#de,e.index)}reset(){this.zeroTime=l.Types.Timing.MilliSeconds(0),this.contentElement.removeChildren(),this.contentElement.appendChild(this.statusLabel)}setStatusText(e){this.statusLabel.textContent=e}}class q{fragment;widget;index;dialog=null;#ce;static fromFilmStrip(e,t){const i={source:"TraceEngine",frames:e.frames,index:t,zeroTime:l.Helpers.Timing.microSecondsToMilliseconds(e.zeroTime)};return new q(i)}constructor(e){this.#ce=e,this.index=e.index;const t=o.UIUtils.createTextButton("◀",this.onPrevFrame.bind(this));o.Tooltip.Tooltip.install(t,K(j.previousFrame));const i=o.UIUtils.createTextButton("▶",this.onNextFrame.bind(this));o.Tooltip.Tooltip.install(i,K(j.nextFrame)),this.fragment=o.Fragment.Fragment.build`
      <x-widget flex=none margin=12px>
        <x-hbox overflow=auto border='1px solid #ddd'>
          <img $='image' data-film-strip-dialog-img style="max-height: 80vh; max-width: 80vw;"></img>
        </x-hbox>
        <x-hbox x-center justify-content=center margin-top=10px>
          ${t}
          <x-hbox $='time' margin=8px></x-hbox>
          ${i}
        </x-hbox>
      </x-widget>
    `,this.widget=this.fragment.element(),this.widget.tabIndex=0,this.widget.addEventListener("keydown",this.keyDown.bind(this),!1),this.dialog=null,this.render()}hide(){this.dialog&&this.dialog.hide()}#ue(){return this.#ce.frames.length}#me(){return this.#ce.zeroTime}resize(){this.dialog||(this.dialog=new o.Dialog.Dialog,this.dialog.contentElement.appendChild(this.widget),this.dialog.setDefaultFocusedElement(this.widget),this.dialog.show()),this.dialog.setSizeBehavior("MeasureContent")}keyDown(e){const t=e;switch(t.key){case"ArrowLeft":h.Platform.isMac()&&t.metaKey?this.onFirstFrame():this.onPrevFrame();break;case"ArrowRight":h.Platform.isMac()&&t.metaKey?this.onLastFrame():this.onNextFrame();break;case"Home":this.onFirstFrame();break;case"End":this.onLastFrame()}}onPrevFrame(){this.index>0&&--this.index,this.render()}onNextFrame(){this.index<this.#ue()-1&&++this.index,this.render()}onFirstFrame(){this.index=0,this.render()}onLastFrame(){this.index=this.#ue()-1,this.render()}render(){const t=this.#ce.frames[this.index],i=l.Helpers.Timing.microSecondsToMilliseconds(t.screenshotEvent.ts);this.fragment.$("time").textContent=e.TimeUtilities.millisToString(i-this.#me());const s=this.fragment.$("image");s.setAttribute("data-frame-index",this.index.toString()),Z.setImageData(s,t.screenshotEvent.args.dataUri),this.resize()}}var Q=Object.freeze({__proto__:null,FilmStripView:Z,Dialog:q});var J=Object.freeze({__proto__:null,GCActionDelegate:class{handleAction(e,t){for(const e of c.TargetManager.TargetManager.instance().models(c.HeapProfilerModel.HeapProfilerModel))e.collectGarbage();return!0}}});let ee,te;class ie{helper;constructor(){this.helper=new re("performance")}static instance(e={forceNew:null}){const{forceNew:t}=e;return ee&&!t||(ee=new ie),ee}reset(){this.helper.reset()}appendLegacyCPUProfile(e,t){const i=[e.profileHead],s=(e.profileEndTime-e.profileStartTime)/e.totalHitCount;for(;i.length;){const e=i.pop().children;for(let r=0;r<e.length;++r){const n=e[r];if(i.push(n),n.url&&n.positionTicks)for(let e=0;e<n.positionTicks.length;++e){const i=n.positionTicks[e],r=i.line,o=i.ticks*s;this.helper.addLineData(t,n.url,r,o)}}}}appendCPUProfile(e,t){if(!e.lines)return this.appendLegacyCPUProfile(e,t),void this.helper.scheduleUpdate();if(e.samples){for(let i=1;i<e.samples.length;++i){const s=e.lines[i];if(!s)continue;const r=e.nodeByIndex(i);if(!r)continue;const n=Number(r.scriptId)||r.url;if(!n)continue;const o=e.timestamps[i]-e.timestamps[i-1];this.helper.addLineData(t,n,s,o)}this.helper.scheduleUpdate()}}}class se{helper;constructor(){this.helper=new re("memory")}static instance(e={forceNew:null}){const{forceNew:t}=e;return te&&!t||(te=new se),te}reset(){this.helper.reset()}appendHeapProfile(e,t){const i=this.helper;!function e(s){if(s.children.forEach(e),!s.selfSize)return;const r=Number(s.callFrame.scriptId)||s.callFrame.url;if(!r)return;const n=s.callFrame.lineNumber+1;i.addLineData(t,r,n,s.selfSize)}(e.head),i.scheduleUpdate()}}class re{type;locationPool;updateTimer;lineData;constructor(e){this.type=e,this.locationPool=new u.LiveLocation.LiveLocationPool,this.updateTimer=null,this.reset()}reset(){this.lineData=new Map,this.scheduleUpdate()}addLineData(e,t,i,s){let r=this.lineData.get(e);r||(r=new Map,this.lineData.set(e,r));let n=r.get(t);n||(n=new Map,r.set(t,n)),n.set(i,(n.get(i)||0)+s)}scheduleUpdate(){this.updateTimer||(this.updateTimer=window.setTimeout((()=>{this.updateTimer=null,this.doUpdate()}),0))}async doUpdate(){this.locationPool.disposeAll();const e=new Map,t=[];for(const[i,s]of this.lineData){const r=i?i.model(c.DebuggerModel.DebuggerModel):null;for(const[i,n]of s){const s=m.Workspace.WorkspaceImpl.instance();if(r){const s=u.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance();for(const o of n){const n=o[0]-1,a=o[1],l="string"==typeof i?r.createRawLocationByURL(i,n,0):r.createRawLocationByScriptId(String(i),n,0);l&&t.push(s.rawLocationToUILocation(l).then((t=>{if(t){let i=e.get(t.uiSourceCode);i||(i=new Map,e.set(t.uiSourceCode,i)),i.set(t.lineNumber+1,a)}})))}}else if("string"==typeof i){const t=s.uiSourceCodeForURL(i);t&&e.set(t,n)}}await Promise.all(t);for(const[t,i]of e)t.setDecorationData(this.type,i)}for(const t of m.Workspace.WorkspaceImpl.instance().uiSourceCodes())e.has(t)||t.setDecorationData(this.type,void 0)}}var ne=Object.freeze({__proto__:null,Performance:ie,Memory:se,Helper:re});let oe;class ae{running;sessionId;loadEventCallback;setting;constructor(){this.running=!1,this.sessionId=0,this.loadEventCallback=()=>{},this.setting=s.Settings.Settings.instance().moduleSetting("memory-live-heap-profile"),this.setting.addChangeListener((e=>e.data?this.startProfiling():this.stopProfiling())),this.setting.get()&&this.startProfiling()}static instance(e={forceNew:null}){const{forceNew:t}=e;return oe&&!t||(oe=new ae),oe}async run(){}modelAdded(e){e.startSampling(1e4)}modelRemoved(e){}async startProfiling(){if(this.running)return;this.running=!0;const e=this.sessionId;c.TargetManager.TargetManager.instance().observeModels(c.HeapProfilerModel.HeapProfilerModel,this),c.TargetManager.TargetManager.instance().addModelListener(c.ResourceTreeModel.ResourceTreeModel,c.ResourceTreeModel.Events.Load,this.loadEventFired,this);do{const t=c.TargetManager.TargetManager.instance().models(c.HeapProfilerModel.HeapProfilerModel),i=await Promise.all(t.map((e=>e.getSamplingProfile())));if(e!==this.sessionId)break;se.instance().reset();for(let e=0;e<i.length;++e){const s=i[e];s&&se.instance().appendHeapProfile(s,t[e].target())}await Promise.race([new Promise((e=>window.setTimeout(e,h.InspectorFrontendHost.isUnderTest()?10:5e3))),new Promise((e=>{this.loadEventCallback=e}))])}while(e===this.sessionId);c.TargetManager.TargetManager.instance().unobserveModels(c.HeapProfilerModel.HeapProfilerModel,this),c.TargetManager.TargetManager.instance().removeModelListener(c.ResourceTreeModel.ResourceTreeModel,c.ResourceTreeModel.Events.Load,this.loadEventFired,this);for(const e of c.TargetManager.TargetManager.instance().models(c.HeapProfilerModel.HeapProfilerModel))e.stopSampling();se.instance().reset()}stopProfiling(){this.running&&(this.running=!1,this.sessionId++)}loadEventFired(){this.loadEventCallback()}}var le=Object.freeze({__proto__:null,LiveHeapProfile:ae});const he={lowest:"Lowest",low:"Low",medium:"Medium",high:"High",highest:"Highest"},de=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/NetworkPriorities.ts",he),ce=e.i18n.getLocalizedString.bind(void 0,de);const ue=new Map;let me;function ge(){if(me)return me;const e=new Map;return e.set("VeryLow",ce(he.lowest)),e.set("Low",ce(he.low)),e.set("Medium",ce(he.medium)),e.set("High",ce(he.high)),e.set("VeryHigh",ce(he.highest)),me=e,e}const pe=new Map;var fe=Object.freeze({__proto__:null,uiLabelForNetworkPriority:function(e){return ge().get(e)||""},uiLabelToNetworkPriority:function(e){return 0===ue.size&&ge().forEach(((e,t)=>ue.set(e,t))),ue.get(e)||""},priorityUILabelMap:ge,networkPriorityWeight:function(e){return 0===pe.size&&(pe.set("VeryLow",1),pe.set("Low",2),pe.set("Medium",3),pe.set("High",4),pe.set("VeryHigh",5)),pe.get(e)||0}}),ve={cssContent:'.overview-grid-window-selector{position:absolute;top:0;bottom:0;background-color:var(--sys-color-state-ripple-primary);z-index:250;pointer-events:none}.overview-grid-window-resizer{position:absolute;top:0;height:19px;width:10px;margin-left:-5px;background-color:var(--sys-color-tonal-container);border:1px solid var(--sys-color-tonal-outline);z-index:500;border-radius:3px}.overview-grid-window-resizer::before,\n.overview-grid-window-resizer::after{content:"";width:1px;background:var(--sys-color-primary);height:7px;position:absolute;left:2px;top:5px;border-radius:1px}.overview-grid-window-resizer::after{left:5px}.overview-grid-window-resizer:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.overview-grid-cursor-area{position:absolute;left:0;right:0;top:20px;bottom:0;z-index:500;cursor:text}.overview-grid-cursor-position{position:absolute;top:0;bottom:0;width:2px;background-color:var(--sys-color-primary);z-index:500;pointer-events:none;visibility:hidden;overflow:hidden}.window-curtain-left,\n.window-curtain-right{background-color:var(--sys-color-state-ripple-primary);position:absolute;top:0;height:100%;z-index:300;pointer-events:none;border:2px none var(--sys-color-tonal-outline)}.window-curtain-left{left:0;border-right-style:solid}.window-curtain-right{right:0;border-left-style:solid}.create-breadcrumb-button-container{visibility:hidden;opacity:0%;transition:opacity 100ms 250ms;display:flex;position:absolute;top:15px;justify-content:center;z-index:600;left:0;right:0}.is-breadcrumb-button-visible{visibility:visible;opacity:100%}.create-breadcrumb-button-container.with-screenshots{top:20px}.create-breadcrumb-button{display:flex;flex-shrink:0;align-items:center;background:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow-depth-3);border-radius:50px;padding:5px 10px;gap:2px}.create-breadcrumb-button:active{cursor:default}.create-breadcrumb-button:hover{background:var(--sys-color-neutral-container)}@media (forced-colors: active){.overview-grid-cursor-position{forced-color-adjust:none;background-color:Highlight}.window-curtain-left,\n  .window-curtain-right{background-color:transparent;border-color:ButtonText}.overview-grid-window-resizer{background-color:ButtonText}.overview-grid-window-resizer:hover,\n  .overview-grid-window-resizer:active,\n  .overview-grid-window-resizer:focus-visible{forced-color-adjust:none;background-color:Highlight}}'};const we={overviewGridWindow:"Overview grid window",leftResizer:"Left Resizer",rightResizer:"Right Resizer"},be=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/OverviewGrid.ts",we),ye=e.i18n.getLocalizedString.bind(void 0,be);class Te{element;grid;window;constructor(e,t){this.element=document.createElement("div"),this.element.id=e+"-overview-container",this.grid=new M,this.grid.element.id=e+"-overview-grid",this.grid.setScrollTop(0),this.element.appendChild(this.grid.element),this.window=new Ce(this.element,this.grid.dividersLabelBarElement,t)}enableCreateBreadcrumbsButton(){return this.window.enableCreateBreadcrumbsButton()}set showingScreenshots(e){this.window.showingScreenshots=e}clientWidth(){return this.element.clientWidth}updateDividers(e){this.grid.updateDividers(e)}addEventDividers(e){this.grid.addEventDividers(e)}removeEventDividers(){this.grid.removeEventDividers()}reset(){this.window.reset()}windowLeft(){return this.window.windowLeft||0}windowRight(){return this.window.windowRight||0}setWindow(e,t){this.window.setWindow(e,t)}addEventListener(e,t,i){return this.window.addEventListener(e,t,i)}setClickHandler(e){this.window.setClickHandler(e)}zoom(e,t){this.window.zoom(e,t)}setResizeEnabled(e){this.window.setEnabled(e)}}const xe=14,Ee=3.5,Se=10;class Ce extends s.ObjectWrapper.ObjectWrapper{parentElement;calculator;leftResizeElement;rightResizeElement;leftCurtainElement;rightCurtainElement;breadcrumbButtonContainerElement;createBreadcrumbButton;curtainsRange;breadcrumbZoomIcon;overviewWindowSelector;offsetLeft;dragStartPoint;dragStartLeft;dragStartRight;windowLeft;windowRight;enabled;clickHandler;resizerParentOffsetLeft;#ge=!1;#pe=!1;constructor(e,t,s){super(),this.parentElement=e,this.parentElement.classList.add("parent-element"),o.ARIAUtils.markAsGroup(this.parentElement),this.calculator=s,o.ARIAUtils.setLabel(this.parentElement,ye(we.overviewGridWindow)),o.UIUtils.installDragHandle(this.parentElement,this.startWindowSelectorDragging.bind(this),this.windowSelectorDragging.bind(this),this.endWindowSelectorDragging.bind(this),"text",null),t&&o.UIUtils.installDragHandle(t,this.startWindowDragging.bind(this),this.windowDragging.bind(this),null,"-webkit-grabbing","-webkit-grab"),this.parentElement.addEventListener("wheel",this.onMouseWheel.bind(this),!0),this.parentElement.addEventListener("dblclick",this.resizeWindowMaximum.bind(this),!0),i.ThemeSupport.instance().appendStyle(this.parentElement,ve),this.leftResizeElement=e.createChild("div","overview-grid-window-resizer"),o.UIUtils.installDragHandle(this.leftResizeElement,this.resizerElementStartDragging.bind(this),this.leftResizeElementDragging.bind(this),null,"ew-resize"),this.rightResizeElement=e.createChild("div","overview-grid-window-resizer"),o.UIUtils.installDragHandle(this.rightResizeElement,this.resizerElementStartDragging.bind(this),this.rightResizeElementDragging.bind(this),null,"ew-resize"),o.ARIAUtils.setLabel(this.leftResizeElement,ye(we.leftResizer)),o.ARIAUtils.markAsSlider(this.leftResizeElement);this.leftResizeElement.addEventListener("keydown",(e=>this.handleKeyboardResizing(e,!1))),o.ARIAUtils.setLabel(this.rightResizeElement,ye(we.rightResizer)),o.ARIAUtils.markAsSlider(this.rightResizeElement);this.rightResizeElement.addEventListener("keydown",(e=>this.handleKeyboardResizing(e,!0))),this.rightResizeElement.addEventListener("focus",this.onRightResizeElementFocused.bind(this)),this.leftCurtainElement=e.createChild("div","window-curtain-left"),this.rightCurtainElement=e.createChild("div","window-curtain-right"),this.breadcrumbButtonContainerElement=e.createChild("div","create-breadcrumb-button-container"),this.createBreadcrumbButton=this.breadcrumbButtonContainerElement.createChild("div","create-breadcrumb-button"),this.reset()}enableCreateBreadcrumbsButton(){return this.curtainsRange=this.createBreadcrumbButton.createChild("div"),this.breadcrumbZoomIcon=new g.Icon.Icon,this.breadcrumbZoomIcon.data={iconName:"zoom-in",color:"var(--icon-default)",width:"20px",height:"20px"},this.createBreadcrumbButton.appendChild(this.breadcrumbZoomIcon),this.createBreadcrumbButton.addEventListener("click",(()=>{this.#fe()})),this.#ge=!0,this.#ve(this.parentElement),this.#ve(this.rightResizeElement),this.#ve(this.leftResizeElement),this.breadcrumbButtonContainerElement}set showingScreenshots(e){this.breadcrumbButtonContainerElement.classList.toggle("with-screenshots",e)}#ve(e){this.#ge&&(e.addEventListener("mouseover",(()=>{(this.windowLeft??0)<=0&&(this.windowRight??1)>=1?(this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1),this.#pe=!1):(this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0),this.#pe=!0)})),e.addEventListener("mouseout",(()=>{this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1),this.#pe=!1})))}onRightResizeElementFocused(){this.parentElement.scrollLeft=0}reset(){this.windowLeft=0,this.windowRight=1,this.setEnabled(!0),this.updateCurtains()}setEnabled(e){this.enabled=e,this.rightResizeElement.tabIndex=e?0:-1,this.leftResizeElement.tabIndex=e?0:-1}setClickHandler(e){this.clickHandler=e}resizerElementStartDragging(e){const t=e,i=e.target;return!!this.enabled&&(this.resizerParentOffsetLeft=t.pageX-t.offsetX-i.offsetLeft,e.stopPropagation(),!0)}leftResizeElementDragging(e){const t=e;this.resizeWindowLeft(t.pageX-(this.resizerParentOffsetLeft||0)),e.preventDefault()}rightResizeElementDragging(e){const t=e;this.resizeWindowRight(t.pageX-(this.resizerParentOffsetLeft||0)),e.preventDefault()}handleKeyboardResizing(e,t){const i=e,s=e.target;let r=!1;if("ArrowLeft"===i.key||"ArrowRight"===i.key){"ArrowRight"===i.key&&(r=!0);const n=this.getNewResizerPosition(s.offsetLeft,r,i.ctrlKey);t?this.resizeWindowRight(n):this.resizeWindowLeft(n),e.consume(!0)}}getNewResizerPosition(e,t,i){let s,r=i?10:2;r=t?r:-Math.abs(r);return s=e+Ee+r,t&&s<Se?s=Se:!t&&s>this.parentElement.clientWidth-Se&&(s=this.parentElement.clientWidth-Se),s}startWindowSelectorDragging(e){if(!this.enabled)return!1;const t=e;this.offsetLeft=this.parentElement.getBoundingClientRect().left;const i=t.x-this.offsetLeft;return this.overviewWindowSelector=new Le(this.parentElement,i),!0}windowSelectorDragging(e){if(this.#pe=!0,!this.overviewWindowSelector)return;const t=e;this.overviewWindowSelector.updatePosition(t.x-this.offsetLeft),e.preventDefault()}endWindowSelectorDragging(e){if(!this.overviewWindowSelector)return;const t=e,i=this.overviewWindowSelector.close(t.x-this.offsetLeft);if(this.#ge&&i.start===i.end)return;delete this.overviewWindowSelector;if(i.end-i.start<3){if(this.clickHandler&&this.clickHandler.call(null,e))return;const t=i.end;i.start=Math.max(0,t-7),i.end=Math.min(this.parentElement.clientWidth,t+7)}else i.end-i.start<xe&&(this.parentElement.clientWidth-i.end>xe?i.end=i.start+xe:i.start=i.end-xe);this.setWindowPosition(i.start,i.end)}startWindowDragging(e){const t=e;return this.dragStartPoint=t.pageX,this.dragStartLeft=this.windowLeft||0,this.dragStartRight=this.windowRight||0,e.stopPropagation(),!0}windowDragging(e){this.#pe=!0,this.#ge&&this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0);const t=e;t.preventDefault();let i=(t.pageX-this.dragStartPoint)/this.parentElement.clientWidth;this.dragStartLeft+i<0&&(i=-this.dragStartLeft),this.dragStartRight+i>1&&(i=1-this.dragStartRight),this.setWindow(this.dragStartLeft+i,this.dragStartRight+i)}resizeWindowLeft(e){this.#pe=!0,e<Se?e=0:e>this.rightResizeElement.offsetLeft-4&&(e=this.rightResizeElement.offsetLeft-4),this.setWindowPosition(e,null)}resizeWindowRight(e){this.#pe=!0,e>this.parentElement.clientWidth-Se?e=this.parentElement.clientWidth:e<this.leftResizeElement.offsetLeft+xe&&(e=this.leftResizeElement.offsetLeft+xe),this.setWindowPosition(null,e)}resizeWindowMaximum(){this.setWindowPosition(0,this.parentElement.clientWidth)}getRawSliderValue(e){if(!this.calculator)throw new Error("No calculator to calculate boundaries");const t=this.calculator.minimumBoundary(),i=this.calculator.maximumBoundary()-t;return e?t+i*(this.windowLeft||0):t+i*(this.windowRight||0)}updateResizeElementPositionValue(e,t){const i=e.toFixed(2),s=t.toFixed(2);o.ARIAUtils.setAriaValueNow(this.leftResizeElement,i),o.ARIAUtils.setAriaValueNow(this.rightResizeElement,s);const r=Number(s)-.5,n=Number(i)+.5;o.ARIAUtils.setAriaValueMinMax(this.leftResizeElement,"0",r.toString()),o.ARIAUtils.setAriaValueMinMax(this.rightResizeElement,n.toString(),"100")}updateResizeElementPositionLabels(){if(!this.calculator)return;const e=this.calculator.formatValue(l.Types.Timing.MilliSeconds(this.getRawSliderValue(!0))),t=this.calculator.formatValue(l.Types.Timing.MilliSeconds(this.getRawSliderValue(!1)));o.ARIAUtils.setAriaValueText(this.leftResizeElement,String(e)),o.ARIAUtils.setAriaValueText(this.rightResizeElement,String(t))}updateResizeElementPercentageLabels(e,t){o.ARIAUtils.setAriaValueText(this.leftResizeElement,e),o.ARIAUtils.setAriaValueText(this.rightResizeElement,t)}calculateWindowPosition(){return{rawStartValue:Number(this.getRawSliderValue(!0)),rawEndValue:Number(this.getRawSliderValue(!1))}}setWindow(e,t){this.windowLeft=e,this.windowRight=t,this.updateCurtains(),this.calculator&&this.dispatchEventToListeners("WindowChangedWithPosition",this.calculateWindowPosition()),this.dispatchEventToListeners("WindowChanged"),this.#we(e,t)}#we(e,t){this.#ge&&(t>=1&&e<=0||!this.#pe?this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!1):this.breadcrumbButtonContainerElement.classList.toggle("is-breadcrumb-button-visible",!0))}#fe(){this.dispatchEventToListeners("BreadcrumbAdded",this.calculateWindowPosition())}updateCurtains(){const e=this.windowLeft||0,t=this.windowRight||0;let i=e,s=t;const r=s-i;if(0!==this.parentElement.clientWidth){const n=r*this.parentElement.clientWidth,o=7;if(n<o){const a=o/n;i=(t+e-r*a)/2,s=(t+e+r*a)/2}}const n=100*i,o=100*s,a=100-100*s,l=n+"%",h=o+"%";this.leftResizeElement.style.left=l,this.rightResizeElement.style.left=h,this.leftCurtainElement.style.width=l,this.rightCurtainElement.style.width=a+"%",this.breadcrumbButtonContainerElement.style.marginLeft=n>0?n+"%":"0%",this.breadcrumbButtonContainerElement.style.marginRight=a>0?a+"%":"0%",this.curtainsRange&&(this.curtainsRange.textContent=this.getWindowRange().toFixed(0)+" ms"),this.updateResizeElementPositionValue(n,o),this.calculator?this.updateResizeElementPositionLabels():this.updateResizeElementPercentageLabels(l,h),this.toggleZoomButtonDisplay()}toggleZoomButtonDisplay(){this.breadcrumbZoomIcon&&(this.getWindowRange()<4.5?(this.breadcrumbZoomIcon.style.display="none",this.breadcrumbButtonContainerElement.style.pointerEvents="none"):(this.breadcrumbZoomIcon.style.display="flex",this.breadcrumbButtonContainerElement.style.pointerEvents="auto"))}getWindowRange(){if(!this.calculator)throw new Error("No calculator to calculate window range");const e=this.windowLeft&&this.windowLeft>0?this.windowLeft:0,t=this.windowRight&&this.windowRight<1?this.windowRight:1;return this.calculator.boundarySpan()*(t-e)}setWindowPosition(e,t){const i=this.parentElement.clientWidth,s="number"==typeof e?e/i:this.windowLeft,r="number"==typeof t?t/i:this.windowRight;this.setWindow(s||0,r||0)}onMouseWheel(e){const t=e;if(this.enabled){if(t.deltaY){const e=1.1,i=1/53,s=t.offsetX/this.parentElement.clientWidth;this.zoom(Math.pow(e,t.deltaY*i),s)}if(t.deltaX){let e=Math.round(.3*t.deltaX);const i=this.leftResizeElement.offsetLeft+Ee,s=this.rightResizeElement.offsetLeft+Ee;i-e<0&&(e=i),s-e>this.parentElement.clientWidth&&(e=s-this.parentElement.clientWidth),this.setWindowPosition(i-e,s-e),t.preventDefault()}}}zoom(e,t){let i=this.windowLeft||0,s=this.windowRight||0;const n=s-i;let o=e*n;o>1&&(o=1,e=o/n),i=t+(i-t)*e,i=r.NumberUtilities.clamp(i,0,1-o),s=t+(s-t)*e,s=r.NumberUtilities.clamp(s,o,1),this.setWindow(i,s)}}class Le{startPosition;width;windowSelector;constructor(e,t){this.startPosition=t,this.width=e.offsetWidth,this.windowSelector=document.createElement("div"),this.windowSelector.className="overview-grid-window-selector",this.windowSelector.style.left=this.startPosition+"px",this.windowSelector.style.right=this.width-this.startPosition+"px",e.appendChild(this.windowSelector)}close(e){return e=Math.max(0,Math.min(e,this.width)),this.windowSelector.remove(),this.startPosition<e?{start:this.startPosition,end:e}:{start:e,end:this.startPosition}}updatePosition(e){(e=Math.max(0,Math.min(e,this.width)))<this.startPosition?(this.windowSelector.style.left=e+"px",this.windowSelector.style.right=this.width-this.startPosition+"px"):(this.windowSelector.style.left=this.startPosition+"px",this.windowSelector.style.right=this.width-e+"px")}}var Pe=Object.freeze({__proto__:null,OverviewGrid:Te,MinSelectableSize:xe,WindowScrollSpeedFactor:.3,ResizerOffset:Ee,OffsetFromWindowEnds:Se,Window:Ce,WindowSelector:Le});const ke=new CSSStyleSheet;ke.replaceSync(".root{align-items:flex-start;display:flex;min-width:fit-content;white-space:nowrap}.chart-root{position:relative;overflow:hidden}.pie-chart-foreground{position:absolute;width:100%;height:100%;z-index:10;top:0;display:flex;pointer-events:none}.pie-chart-total{margin:auto;padding:2px 5px;pointer-events:auto}:focus{outline-width:0}.pie-chart-total.selected{font-weight:bold}.chart-root .slice.selected{stroke:var(--sys-color-primary);stroke-opacity:1;stroke-width:0.04;stroke-linecap:round;stroke-linejoin:round}.pie-chart-legend{margin-left:30px}.pie-chart-legend-row{margin:5px 2px 5px auto;padding-right:25px}.pie-chart-legend-row.selected{font-weight:bold}.pie-chart-legend-row:focus-visible{box-shadow:0 0 0 2px var(--sys-color-state-focus-ring)!important}.pie-chart-swatch{display:inline-block;width:11px;height:11px;margin:0 6px;top:1px;position:relative;border:1px solid var(--sys-color-neutral-outline)}.pie-chart-name{display:inline-block}.pie-chart-size{display:inline-block;text-align:right;width:70px}@media (forced-colors: active){.pie-chart-swatch{forced-color-adjust:none;border-color:ButtonText}.pie-chart-total{forced-color-adjust:none;background-color:canvas}}\n/*# sourceURL=pieChart.css */\n");const{render:De,html:Re,svg:Ie}=p,Me={total:"Total"},He=e.i18n.registerUIStrings("ui/legacy/components/perf_ui/PieChart.ts",Me),Be=e.i18n.getLocalizedString.bind(void 0,He);class Oe extends HTMLElement{static litTagName=p.literal`devtools-perf-piechart`;shadow=this.attachShadow({mode:"open"});chartName="";size=0;formatter=e=>String(e);showLegend=!1;total=0;slices=[];totalSelected=!0;sliceSelected=-1;innerR=.618;lastAngle=-Math.PI/2;connectedCallback(){this.shadow.adoptedStyleSheets=[ke]}set data(e){this.chartName=e.chartName,this.size=e.size,this.formatter=e.formatter,this.showLegend=e.showLegend,this.total=e.total,this.slices=e.slices,this.render()}render(){this.lastAngle=-Math.PI/2;const e=Re`
      <div class="root" role="group" @keydown=${this.onKeyDown} aria-label=${this.chartName}
          jslog=${d.pieChart().track({keydown:"ArrowUp|ArrowDown"})}>
        <div class="chart-root" style="width: ${this.size}px; height: ${this.size}px;">
          ${Ie`
          <svg>
          <g transform="scale(${this.size/2}) translate(1, 1) scale(0.99, 0.99)">
            <circle r="1" stroke="hsl(0, 0%, 80%)" fill="transparent" stroke-width=${1/this.size}></circle>
            <circle r=${this.innerR} stroke="hsl(0, 0%, 80%)" fill="transparent" stroke-width=${1/this.size}></circle>
            ${this.slices.map(((e,t)=>{const i=this.sliceSelected===t,s=i&&!this.showLegend?"0":"-1";return Ie`<path class="slice ${i?"selected":""}"
                  jslog=${d.pieChartSlice().track({click:!0})}
                  @click=${this.onSliceClicked(t)} tabIndex=${s}
                  fill=${e.color} d=${this.getPathStringForSlice(e)}
                  aria-label=${e.title} id=${i?"selectedSlice":""}></path>`}))}
            <!-- This is so that the selected slice is re-drawn on top, to avoid re-ordering slices
            just to render them properly. -->
            <use href="#selectedSlice" />
            </g>
          </svg>
          `}
          <div class="pie-chart-foreground">
            <div class="pie-chart-total ${this.totalSelected?"selected":""}" @click=${this.selectTotal}
                jslog=${d.pieChartTotal("select-total").track({click:!0})}
                tabIndex=${this.totalSelected&&!this.showLegend?"1":"-1"}>
              ${this.total?this.formatter(this.total):""}
            </div>
          </div>
        </div>
        ${this.showLegend?Re`
        <div class="pie-chart-legend" jslog=${d.section("legend")}>
          ${this.slices.map(((e,t)=>{const i=this.sliceSelected===t;return Re`
              <div class="pie-chart-legend-row ${i?"selected":""}"
                  jslog=${d.pieChartSlice().track({click:!0})}
                  @click=${this.onSliceClicked(t)} tabIndex=${i?"0":"-1"}>
                <div class="pie-chart-size">${this.formatter(e.value)}</div>
                <div class="pie-chart-swatch" style="background-color: ${e.color};"></div>
                <div class="pie-chart-name">${e.title}</div>
              </div>`}))}
          <div class="pie-chart-legend-row ${this.totalSelected?"selected":""}"
              jslog=${d.pieChartTotal("select-total").track({click:!0})}
              @click=${this.selectTotal} tabIndex=${this.totalSelected?"0":"-1"}>
            <div class="pie-chart-size">${this.formatter(this.total)}</div>
            <div class="pie-chart-swatch"></div>
            <div class="pie-chart-name">${Be(Me.total)}</div>
          </div>
        </div>
        `:""}
    `;De(e,this.shadow,{host:this})}onSliceClicked(e){return()=>{this.selectSlice(e)}}selectSlice(e){this.totalSelected=!1,this.sliceSelected=e,this.render()}selectTotal(){this.totalSelected=!0,this.sliceSelected=-1,this.render()}selectAndFocusTotal(){this.selectTotal();const e=this.shadow.querySelector(".pie-chart-legend > :last-child");e&&e.focus()}selectAndFocusSlice(e){this.selectSlice(e);const t=this.shadow.querySelector(`.pie-chart-legend > :nth-child(${e+1})`);t&&t.focus()}focusNextElement(){this.totalSelected?this.selectAndFocusSlice(0):this.sliceSelected===this.slices.length-1?this.selectAndFocusTotal():this.selectAndFocusSlice(this.sliceSelected+1)}focusPreviousElement(){this.totalSelected?this.selectAndFocusSlice(this.slices.length-1):0===this.sliceSelected?this.selectAndFocusTotal():this.selectAndFocusSlice(this.sliceSelected-1)}onKeyDown(e){let t=!1;"ArrowDown"===e.key?(this.focusNextElement(),t=!0):"ArrowUp"===e.key&&(this.focusPreviousElement(),t=!0),t&&(e.stopImmediatePropagation(),e.preventDefault())}getPathStringForSlice(e){let t=e.value/this.total*2*Math.PI;if(!isFinite(t))return;t=Math.min(t,2*Math.PI*.9999);const i=Math.cos(this.lastAngle),s=Math.sin(this.lastAngle);this.lastAngle+=t;const r=Math.cos(this.lastAngle),n=Math.sin(this.lastAngle),o=this.innerR,a=r*o,l=n*o,h=i*o,d=s*o,c=t>Math.PI?1:0;return`M${i},${s} A1,1,0,${c},1,${r},${n} L${a},${l} A${o},${o},0,${c},0,${h},${d} Z`}}customElements.define("devtools-perf-piechart",Oe);var Ae=Object.freeze({__proto__:null,PieChart:Oe});class Ge{#be=l.Types.Timing.MilliSeconds(0);#ye=l.Types.Timing.MilliSeconds(100);workingArea;navStartTimes;computePosition(e){return(e-this.#be)/this.boundarySpan()*this.workingArea}positionToTime(e){return e/this.workingArea*this.boundarySpan()+this.#be}setBounds(e,t){this.#be=e,this.#ye=t}setNavStartTimes(e){this.navStartTimes=e}setDisplayWidth(e){this.workingArea=e}reset(){this.setBounds(l.Types.Timing.MilliSeconds(0),l.Types.Timing.MilliSeconds(100))}formatValue(t,i){if(this.navStartTimes)for(let e=this.navStartTimes.length-1;e>=0;e--){const i=l.Helpers.Timing.microSecondsToMilliseconds(this.navStartTimes[e].ts);if(t>i){t-=i-this.zeroTime();break}}return e.TimeUtilities.preciseMillisToString(t-this.zeroTime(),i)}maximumBoundary(){return this.#ye}minimumBoundary(){return this.#be}zeroTime(){return this.#be}boundarySpan(){return l.Types.Timing.MilliSeconds(this.#ye-this.#be)}}var ze=Object.freeze({__proto__:null,TimelineOverviewCalculator:Ge});const We=new CSSStyleSheet;We.replaceSync(".overview-info:not(:empty){display:flex;background:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);padding:3px}.overview-info .frame .time{display:none}.overview-info .frame .thumbnail img{max-width:50vw;max-height:50vh}\n/*# sourceURL=timelineOverviewInfo.css */\n");class Fe extends(s.ObjectWrapper.eventMixin(o.Widget.VBox)){overviewCalculator;overviewGrid;cursorArea;cursorElement;overviewControls;markers;overviewInfo;updateThrottler;cursorEnabled;cursorPosition;lastWidth;windowStartTime;windowEndTime;muteOnWindowChanged;constructor(e){super(),this.element.id=e+"-overview-pane",this.overviewCalculator=new Ge,this.overviewGrid=new Te(e,this.overviewCalculator),this.element.appendChild(this.overviewGrid.element),this.cursorArea=this.overviewGrid.element.createChild("div","overview-grid-cursor-area"),this.cursorElement=this.overviewGrid.element.createChild("div","overview-grid-cursor-position"),this.cursorArea.addEventListener("mousemove",this.onMouseMove.bind(this),!0),this.cursorArea.addEventListener("mouseleave",this.hideCursor.bind(this),!0),this.overviewGrid.setResizeEnabled(!1),this.overviewGrid.addEventListener("WindowChangedWithPosition",this.onWindowChanged,this),this.overviewGrid.addEventListener("BreadcrumbAdded",this.onBreadcrumbAdded,this),this.overviewGrid.setClickHandler(this.onClick.bind(this)),this.overviewControls=[],this.markers=new Map,this.overviewInfo=new Ue(this.cursorElement),this.updateThrottler=new s.Throttler.Throttler(100),this.cursorEnabled=!1,this.cursorPosition=0,this.lastWidth=0,this.windowStartTime=0,this.windowEndTime=1/0,this.muteOnWindowChanged=!1}enableCreateBreadcrumbsButton(){const e=this.overviewGrid.enableCreateBreadcrumbsButton();e.addEventListener("mousemove",this.onMouseMove.bind(this),!0),e.addEventListener("mouseleave",this.hideCursor.bind(this),!0)}onMouseMove(e){if(!this.cursorEnabled)return;const t=e,i=e.target.getBoundingClientRect().left-this.cursorArea.getBoundingClientRect().left;this.cursorPosition=t.offsetX+i,this.cursorElement.style.left=this.cursorPosition+"px",this.cursorElement.style.visibility="visible",this.overviewInfo.setContent(this.buildOverviewInfo())}async buildOverviewInfo(){const e=this.element.ownerDocument,t=this.cursorPosition,i=await Promise.all(this.overviewControls.map((e=>e.overviewInfoPromise(t)))),s=e.createDocumentFragment(),r=i.filter((e=>null!==e));return s.append(...r),s}hideCursor(){this.cursorElement.style.visibility="hidden",this.overviewInfo.hide()}wasShown(){this.update()}willHide(){this.overviewInfo.hide()}onResize(){const e=this.element.offsetWidth;e!==this.lastWidth&&(this.lastWidth=e,this.scheduleUpdate())}setOverviewControls(e){for(let e=0;e<this.overviewControls.length;++e)this.overviewControls[e].dispose();for(let t=0;t<e.length;++t)e[t].setCalculator(this.overviewCalculator),e[t].show(this.overviewGrid.element);this.overviewControls=e,this.update()}set showingScreenshots(e){this.overviewGrid.showingScreenshots=e}setBounds(e,t){e===this.overviewCalculator.minimumBoundary()&&t===this.overviewCalculator.maximumBoundary()||(this.overviewCalculator.setBounds(e,t),this.overviewGrid.setResizeEnabled(!0),this.cursorEnabled=!0,this.scheduleUpdate(e,t))}setNavStartTimes(e){this.overviewCalculator.setNavStartTimes(e)}scheduleUpdate(e,t){this.updateThrottler.schedule((async()=>{this.update(e,t)}))}update(e,t){if(this.isShowing()){this.overviewCalculator.setDisplayWidth(this.overviewGrid.clientWidth());for(let i=0;i<this.overviewControls.length;++i)this.overviewControls[i].update(e,t);this.overviewGrid.updateDividers(this.overviewCalculator),this.updateMarkers(),this.updateWindow()}}setMarkers(e){this.markers=e}getMarkers(){return this.markers}updateMarkers(){const e=new Map;for(const t of this.markers.keys()){const i=this.markers.get(t),s=Math.round(this.overviewCalculator.computePosition(l.Types.Timing.MilliSeconds(t)));e.has(s)||(e.set(s,i),i.style.left=s+"px")}this.overviewGrid.removeEventDividers(),this.overviewGrid.addEventDividers([...e.values()])}reset(){this.windowStartTime=0,this.windowEndTime=1/0,this.overviewCalculator.reset(),this.overviewGrid.reset(),this.overviewGrid.setResizeEnabled(!1),this.cursorEnabled=!1,this.hideCursor(),this.markers=new Map;for(const e of this.overviewControls)e.reset();this.overviewInfo.hide(),this.scheduleUpdate()}onClick(e){return this.overviewControls.some((t=>t.onClick(e)))}onBreadcrumbAdded(){this.dispatchEventToListeners("OverviewPaneBreadcrumbAdded",{startTime:l.Types.Timing.MilliSeconds(this.windowStartTime),endTime:l.Types.Timing.MilliSeconds(this.windowEndTime)})}onWindowChanged(e){if(this.muteOnWindowChanged)return;if(!this.overviewControls.length)return;this.windowStartTime=e.data.rawStartValue===this.overviewCalculator.minimumBoundary()?0:e.data.rawStartValue,this.windowEndTime=e.data.rawEndValue===this.overviewCalculator.maximumBoundary()?1/0:e.data.rawEndValue;const t={startTime:l.Types.Timing.MilliSeconds(this.windowStartTime),endTime:l.Types.Timing.MilliSeconds(this.windowEndTime)};this.dispatchEventToListeners("OverviewPaneWindowChanged",t)}setWindowTimes(e,t){e===this.windowStartTime&&t===this.windowEndTime||(this.windowStartTime=e,this.windowEndTime=t,this.updateWindow(),this.dispatchEventToListeners("OverviewPaneWindowChanged",{startTime:l.Types.Timing.MilliSeconds(e),endTime:l.Types.Timing.MilliSeconds(t)}))}updateWindow(){if(!this.overviewControls.length)return;const e=this.overviewCalculator.minimumBoundary(),t=this.overviewCalculator.maximumBoundary()-e,i=e>0,s=i&&this.windowStartTime?Math.min((this.windowStartTime-e)/t,1):0,r=i&&this.windowEndTime<1/0?(this.windowEndTime-e)/t:1;this.muteOnWindowChanged=!0,this.overviewGrid.setWindow(s,r),this.muteOnWindowChanged=!1}}class Ve extends o.Widget.VBox{calculatorInternal;canvas;contextInternal;constructor(){super(),this.calculatorInternal=null,this.canvas=this.element.createChild("canvas","fill"),this.contextInternal=this.canvas.getContext("2d")}width(){return this.canvas.width}height(){return this.canvas.height}context(){if(!this.contextInternal)throw new Error("Unable to retrieve canvas context");return this.contextInternal}calculator(){return this.calculatorInternal}update(){throw new Error("Not implemented")}dispose(){this.detach()}reset(){}async overviewInfoPromise(e){return null}setCalculator(e){this.calculatorInternal=e}onClick(e){return!1}resetCanvas(){this.element.clientWidth&&this.setCanvasSize(this.element.clientWidth,this.element.clientHeight)}setCanvasSize(e,t){this.canvas.width=e*window.devicePixelRatio,this.canvas.height=t*window.devicePixelRatio}}class Ue{anchorElement;glassPane;visible;element;constructor(e){this.anchorElement=e,this.glassPane=new o.GlassPane.GlassPane,this.glassPane.setPointerEventsBehavior("PierceContents"),this.glassPane.setMarginBehavior("Arrow"),this.glassPane.setSizeBehavior("MeasureContent"),this.visible=!1,this.element=o.Utils.createShadowRootWithCoreStyles(this.glassPane.contentElement,{cssFile:[We],delegatesFocus:void 0}).createChild("div","overview-info")}async setContent(e){this.visible=!0;const t=await e;this.visible&&(this.element.removeChildren(),this.element.appendChild(t),this.glassPane.setContentAnchorBox(this.anchorElement.boxInWindow()),this.glassPane.isShowing()||this.glassPane.show(this.anchorElement.ownerDocument))}hide(){this.visible=!1,this.glassPane.hide()}}var Ne=Object.freeze({__proto__:null,TimelineOverviewPane:Fe,TimelineOverviewBase:Ve,OverviewInfo:Ue});export{E as BrickBreaker,X as ChartViewport,Q as FilmStripView,U as FlameChart,D as Font,J as GCActionDelegate,ne as LineLevelProfile,le as LiveHeapProfile,fe as NetworkPriorities,Pe as OverviewGrid,Ae as PieChart,H as TimelineGrid,ze as TimelineOverviewCalculator,Ne as TimelineOverviewPane};
