import*as e from"../../../core/common/common.js";import*as t from"../../../core/host/host.js";var o={cssContent:".webkit-css-property{color:var(--webkit-css-property-color,var(--sys-color-token-property-special))}.webkit-html-comment{color:var(--sys-color-token-comment)}.webkit-html-tag{color:var(--sys-color-token-tag)}.webkit-html-tag-name,\n.webkit-html-close-tag-name{color:var(--sys-color-token-tag)}.webkit-html-pseudo-element{color:var(--sys-color-token-pseudo-element)}.webkit-html-js-node,\n.webkit-html-css-node{color:var(--text-primary);white-space:pre-wrap}.webkit-html-text-node{color:var(--text-primary);unicode-bidi:-webkit-isolate}.webkit-html-entity-value{background-color:rgb(0 0 0/15%);unicode-bidi:-webkit-isolate}.webkit-html-doctype{color:var(--text-secondary)}.webkit-html-attribute-name{color:var(--sys-color-token-attribute);unicode-bidi:-webkit-isolate}.webkit-html-attribute-value{color:var(--sys-color-token-attribute-value);unicode-bidi:-webkit-isolate;word-break:break-all}.devtools-link{color:var(--text-link);text-decoration:underline;outline-offset:2px;.elements-disclosure &{color:var(--text-link)}devtools-icon{vertical-align:baseline;color:var(--sys-color-primary)}:focus .selected & devtools-icon{color:var(--sys-color-tonal-container)}&:focus-visible{outline-width:unset}&.invalid-link{color:var(--text-disabled);text-decoration:none}&:not(.devtools-link-prevent-click, .invalid-link){cursor:pointer}@media (forced-colors: active){&:not(.devtools-link-prevent-click){forced-color-adjust:none;color:linktext}&:focus-visible{background:Highlight;color:HighlightText}}}"};let s;const n=new Map;class r extends EventTarget{setting;themeNameInternal="default";customSheets=new Set;computedRoot=e.Lazy.lazy((()=>window.getComputedStyle(document.documentElement)));constructor(e){super(),this.setting=e}static hasInstance(){return void 0!==s}static instance(e={forceNew:null,setting:null}){const{forceNew:t,setting:o}=e;if(!s||t){if(!o)throw new Error(`Unable to create theme support: setting must be provided: ${(new Error).stack}`);s=new r(o)}return s}getComputedValue(e,t=null){const o=t?window.getComputedStyle(t):this.computedRoot();if("symbol"==typeof o)throw new Error(`Computed value for property (${e}) could not be found on :root.`);let s=n.get(o);s||(s=new Map,n.set(o,s));let r=s.get(e);return r||(r=o.getPropertyValue(e).trim(),r&&s.set(e,r)),r}hasTheme(){return"default"!==this.themeNameInternal}themeName(){return this.themeNameInternal}injectHighlightStyleSheets(e){this.appendStyle(e,o)}appendStyle(e,{cssContent:t}){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}injectCustomStyleSheets(e){for(const t of this.customSheets){const o=document.createElement("style");o.textContent=t,e.appendChild(o)}}addCustomStylesheet(e){this.customSheets.add(e)}applyTheme(e){const t=window.matchMedia("(forced-colors: active)").matches,o=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"default",s="systemPreferred"===this.setting.get()||t;this.themeNameInternal=s?o:this.setting.get();const r=e.documentElement.classList.contains("-theme-with-dark-background");e.documentElement.classList.toggle("-theme-with-dark-background","dark"===this.themeNameInternal);r!==e.documentElement.classList.contains("-theme-with-dark-background")&&(n.clear(),this.customSheets.clear(),this.dispatchEvent(new i)),e.documentElement.classList.add("baseline-grayscale")}static async fetchColors(e){if(t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode())return;if(!e)return;const o=e.createElement("link");o.setAttribute("href",`devtools://theme/colors.css?sets=ui,chrome&version=${(new Date).getTime().toString()}`),o.setAttribute("rel","stylesheet"),o.setAttribute("type","text/css");const s=new Promise((e=>{o.onload=e.bind(this,!0),o.onerror=e.bind(this,!1)})),n=e.querySelector("link[href*='//theme/colors.css']");e.body.appendChild(o),await s&&(n&&n.remove(),r.instance().applyTheme(e))}}class i extends Event{static eventName="themechange";constructor(){super(i.eventName,{bubbles:!0,composed:!0})}}export{i as ThemeChangeEvent,r as ThemeSupport};
