import*as t from"../../../core/common/common.js";import*as e from"../../../core/i18n/i18n.js";import*as o from"../../../models/text_utils/text_utils.js";import*as n from"../../../services/window_bounds/window_bounds.js";import*as r from"../../../third_party/codemirror.next/codemirror.next.js";import*as i from"../../legacy/legacy.js";import*as s from"../code_highlighter/code_highlighter.js";import*as a from"../icon_button/icon_button.js";import*as c from"../../../core/root/root.js";import*as l from"../../../core/sdk/sdk.js";import*as d from"../../../models/bindings/bindings.js";import*as u from"../../../models/javascript_metadata/javascript_metadata.js";import*as m from"../../../models/source_map_scopes/source_map_scopes.js";import*as p from"../../legacy/theme_support/theme_support.js";import*as h from"../../lit-html/lit-html.js";class f{static#t=300;#e;#o=[];#n=1;#r=!1;constructor(t){this.#e=t,this.#o=this.#e.get()}clear(){this.#o=[],this.#e.set([]),this.#n=1}length(){return this.#o.length}pushHistoryItem(t){this.#r&&(this.#o.pop(),this.#r=!1),this.#n=1,t!==this.#i()&&this.#o.push(t),this.#s()}#a(t){this.#r&&this.#o.pop(),this.#r=!0,this.#o.push(t)}previous(t){if(!(this.#n>this.#o.length))return 1===this.#n&&this.#a(t),++this.#n,this.#i()}next(){if(1!==this.#n)return--this.#n,this.#i()}matchingEntries(t,e=50){const o=new Set;for(let n=this.#o.length-1;n>=0&&o.size<e;--n){const e=this.#o[n];e.startsWith(t)&&o.add(e)}return o}#i(){return this.#o[this.#o.length-this.#n]}#s(){this.#e.set(this.#o.slice(-f.#t))}}var g=Object.freeze({__proto__:null,AutocompleteHistory:f});const y=r.EditorView.theme({"&.cm-editor":{color:"color: var(--sys-color-on-subtle)",cursor:"auto","&.cm-focused":{outline:"none"}},".cm-scroller":{lineHeight:"1.4em",fontFamily:"var(--source-code-font-family)",fontSize:"var(--source-code-font-size)"},".cm-content":{lineHeight:"1.4em"},".cm-panels":{backgroundColor:"var(--sys-color-cdt-base-container)"},".cm-panels-bottom":{borderTop:"1px solid var(--sys-color-divider)"},".cm-selectionMatch":{backgroundColor:"var(--sys-color-yellow-container)"},".cm-cursor":{borderLeft:"1px solid var(--sys-color-inverse-surface)"},"&.cm-readonly .cm-cursor":{display:"none"},".cm-cursor-secondary":{borderLeft:"1px solid var(--sys-color-neutral-outline)"},"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"var(--sys-color-tonal-container)"},".cm-selectionBackground":{background:"var(--sys-color-neutral-container)"},".cm-gutters":{borderRight:"none",whiteSpace:"nowrap",backgroundColor:"var(--sys-color-cdt-base-container)"},".cm-gutters .cm-foldGutterElement":{cursor:"pointer",opacity:"0%",transition:"opacity 0.2s"},".cm-gutters .cm-foldGutterElement-folded, .cm-gutters:hover .cm-foldGutterElement":{opacity:"100%"},".cm-lineNumbers":{overflow:"visible",minWidth:"40px"},".cm-lineNumbers .cm-gutterElement":{color:"var(--sys-color-outline)",padding:"0 3px 0 9px"},".cm-foldPlaceholder":{background:"transparent",border:"none",color:"var(--sys-color-token-subtle)"},".cm-matchingBracket, .cm-nonmatchingBracket":{background:"transparent",borderBottom:"none"},"&:focus-within .cm-matchingBracket":{color:"inherit",backgroundColor:"var(--sys-color-surface-variant)",borderBottom:"1px solid var(--sys-color-outline)"},"&:focus-within .cm-nonmatchingBracket":{backgroundColor:"var(--sys-color-error-container)",borderBottom:"1px solid var(--sys-color-error)"},".cm-trailingWhitespace":{backgroundColor:"var(--sys-color-error-container)"},".cm-highlightedTab":{display:"inline-block",position:"relative","&:before":{content:'""',borderBottom:"1px solid var(--sys-color-token-subtle)",position:"absolute",left:"5%",bottom:"50%",width:"90%",pointerEvents:"none"}},".cm-highlightedSpaces:before":{color:"var(--sys-color-token-subtle)",content:"attr(data-display)",position:"absolute",pointerEvents:"none"},".cm-placeholder":{color:"var(--sys-color-token-subtle)"},".cm-completionHint":{color:"var(--sys-color-token-subtle)"},".cm-tooltip":{boxShadow:"var(--drop-shadow)",backgroundColor:"var(--sys-color-neutral-container)"},".cm-argumentHints":{pointerEvents:"none",padding:"0 4px",whiteSpace:"nowrap",lineHeight:"20px",marginBottom:"4px",width:"fit-content"},".cm-tooltip.cm-tooltip-autocomplete > ul":{backgroundColor:"var(--sys-color-cdt-base-container)",maxHeight:"25em",minWidth:"16em","& > li":{display:"flex",justifyContent:"space-between",border:"1px solid var(--sys-color-cdt-base-container)"},"& > li.cm-secondaryCompletion":{display:"flex",backgroundColor:"var(--sys-color-neutral-container)",borderColor:"var(--sys-color-neutral-container)",justifyContent:"space-between","&::before":{content:'">"',fontWeight:"bold",color:"var(--sys-color-primary-bright)",marginRight:"5px"}},"& > li:hover":{backgroundColor:"var(--sys-color-state-hover-on-subtle)"},"& > li[aria-selected]":{backgroundColor:"var(--sys-color-tonal-container)",borderColor:"var(--sys-color-tonal-container)","&, &.cm-secondaryCompletion::before":{color:"var(--sys-color-on-tonal-container)"},"&::after":{content:'"tab"',color:"var(--sys-color-primary-bright)",border:"1px solid var(--sys-color-primary-bright)",borderRadius:"2px",marginLeft:"5px",padding:"1px 3px",fontSize:"10px",lineHeight:"10px"}}},".cm-tooltip.cm-tooltip-autocomplete.cm-conservativeCompletion > ul > li[aria-selected]":{backgroundColor:"var(--sys-color-cdt-base-container)",border:"1px dotted var(--sys-color-on-surface)","&, &.cm-secondaryCompletion::before":{color:"var(--sys-color-on-surface)"},"&::after":{border:"1px solid var(--sys-color-neutral-outline)",color:"var(--sys-color-token-subtle)"}},".cm-completionMatchedText":{textDecoration:"none",fontWeight:"bold"},".cm-highlightedLine":{animation:"cm-fading-highlight 2s 0s"},"@keyframes cm-fading-highlight":{from:{backgroundColor:"var(--sys-color-yellow-container)"},to:{backgroundColor:"transparent"}}}),b={codeEditor:"Code editor",sSuggestionSOfS:"{PH1}, suggestion {PH2} of {PH3}"},v=e.i18n.registerUIStrings("ui/components/text_editor/config.ts",b),w=e.i18n.getLocalizedString.bind(void 0,v),S=[],x=r.Facet.define();class E{settingName;getExtension;compartment=new r.Compartment;constructor(t,e){this.settingName=t,this.getExtension=e}settingValue(){return t.Settings.Settings.instance().moduleSetting(this.settingName).get()}instance(){return[this.compartment.of(this.getExtension(this.settingValue())),x.of(this)]}sync(t,e){const o=this.compartment.get(t),n=this.getExtension(e);return o===n?null:this.compartment.reconfigure(n)}static bool(t,e,o=S){return new E(t,(t=>t?e:o))}static none=[]}const C=E.bool("text-editor-tab-moves-focus",[],r.keymap.of([{key:"Tab",run:t=>!!t.state.doc.length&&r.indentMore(t),shift:t=>!!t.state.doc.length&&r.indentLess(t)}])),k=r.StateEffect.define(),M=r.StateField.define({create:()=>!0,update:(t,e)=>"active"!==r.completionStatus(e.state)||(r.selectedCompletionIndex(e.startState)??0)===(r.selectedCompletionIndex(e.state)??0)&&!e.effects.some((t=>t.is(k)))&&t});function T(t){return!t.state.field(M,!1)&&r.acceptCompletion(t)}function D(t){const e=t.state.selection.main.head,o=t.state.doc.lineAt(e);return!!(e-o.from>=o.length)&&r.acceptCompletion(t)}function j(t,e="option"){return o=>{if("active"!==r.completionStatus(o.state))return!1;if(o.state.field(M,!1))return o.dispatch({effects:k.of(null)}),O(o),!0;const n=r.moveCompletionSelection(t,e)(o);return O(o),n}}function L(){return t=>"active"===r.completionStatus(t.state)&&(r.moveCompletionSelection(!1)(t),O(t),!0)}function O(t){const e=w(b.sSuggestionSOfS,{PH1:r.selectedCompletion(t.state)?.label||"",PH2:(r.selectedCompletionIndex(t.state)||0)+1,PH3:r.currentCompletions(t.state).length});i.ARIAUtils.alert(e)}const P=new E("text-editor-autocompletion",(t=>[r.autocompletion({activateOnTyping:t,icons:!1,optionClass:t=>"secondary"===t.type?"cm-secondaryCompletion":"",tooltipClass:t=>t.field(M,!1)?"cm-conservativeCompletion":"",defaultKeymap:!1,updateSyncTime:100}),r.Prec.highest(r.keymap.of([{key:"End",run:D},{key:"ArrowRight",run:D},{key:"Ctrl-Space",run:r.startCompletion},{key:"Escape",run:r.closeCompletion},{key:"ArrowDown",run:j(!0)},{key:"ArrowUp",run:L()},{mac:"Ctrl-n",run:j(!0)},{mac:"Ctrl-p",run:L()},{key:"PageDown",run:r.moveCompletionSelection(!0,"page")},{key:"PageUp",run:r.moveCompletionSelection(!1,"page")},{key:"Enter",run:T}]))])),I=E.bool("text-editor-bracket-matching",r.bracketMatching()),_=E.bool("text-editor-code-folding",[r.foldGutter({markerDOM(t){const e=t?"triangle-down":"triangle-right",o=new a.Icon.Icon;return o.setAttribute("class",t?"cm-foldGutterElement":"cm-foldGutterElement cm-foldGutterElement-folded"),o.data={iconName:e,color:"var(--icon-fold-marker)",width:"14px",height:"14px"},o}}),r.keymap.of(r.foldKeymap)]),N=r.Prec.highest(r.indentUnit.compute([],(e=>{const n=e.doc.iterLines(1,Math.min(e.doc.lines+1,1e3));return o.TextUtils.detectIndentation(n)??t.Settings.Settings.instance().moduleSetting("text-editor-indent").get()}))),A=E.bool("text-editor-auto-detect-indent",N);function H(t){return r.ViewPlugin.define((e=>({decorations:t.createDeco(e),update(e){this.decorations=t.updateDeco(e,this.decorations)}})),{decorations:t=>t.decorations})}const B=new Map;const R=H(new r.MatchDecorator({regexp:/\t| +/g,decoration:t=>function(t){const e=B.get(t);if(e)return e;const o=r.Decoration.mark({attributes:"\t"===t?{class:"cm-highlightedTab"}:{class:"cm-highlightedSpaces","data-display":"·".repeat(t.length)}});return B.set(t,o),o}(t[0]),boundary:/\S/})),z=H(new r.MatchDecorator({regexp:/\s+$/g,decoration:r.Decoration.mark({class:"cm-trailingWhitespace"}),boundary:/\S/})),W=new E("show-whitespaces-in-editor",(t=>"all"===t?R:"trailing"===t?z:S)),F=E.bool("allow-scroll-past-eof",r.scrollPastEnd()),V=Object.create(null);const $=new E("text-editor-indent",(function(t){let e=V[t];return e||(e=V[t]=r.indentUnit.of(t)),e})),U=E.bool("dom-word-wrap",r.EditorView.lineWrapping);function G(t){return/\r\n/.test(t)&&!/(^|[^\r])\n/.test(t)?r.EditorState.lineSeparator.of("\r\n"):[]}const K=r.keymap.of([{key:"Tab",run:r.acceptCompletion},{key:"Ctrl-m",run:r.cursorMatchingBracket,shift:r.selectMatchingBracket},{key:"Mod-/",run:r.toggleComment},{key:"Mod-d",run:r.selectNextOccurrence},{key:"Alt-ArrowLeft",mac:"Ctrl-ArrowLeft",run:r.cursorSyntaxLeft,shift:r.selectSyntaxLeft},{key:"Alt-ArrowRight",mac:"Ctrl-ArrowRight",run:r.cursorSyntaxRight,shift:r.selectSyntaxRight},{key:"Ctrl-ArrowLeft",mac:"Alt-ArrowLeft",run:r.cursorGroupLeft,shift:r.selectGroupLeft},{key:"Ctrl-ArrowRight",mac:"Alt-ArrowRight",run:r.cursorGroupRight,shift:r.selectGroupRight},...r.standardKeymap,...r.historyKeymap]);function J(){const e=t.Settings.Settings.instance().moduleSetting("ui-theme").get();return"systemPreferred"===e?window.matchMedia("(prefers-color-scheme: dark)").matches:"dark"===e}const q=r.EditorView.theme({},{dark:!0}),Q=new r.Compartment;function X(){return[y,J()?Q.of(q):Q.of([])]}let Y=null;function Z(){return Y||(Y=n.WindowBoundsService.WindowBoundsServiceImpl.instance().getDevToolsBoundingElement()),Y.getBoundingClientRect()}function tt(t){return[X(),r.highlightSpecialChars(),r.highlightSelectionMatches(),r.history(),r.drawSelection(),r.EditorState.allowMultipleSelections.of(!0),r.indentOnInput(),r.syntaxHighlighting(s.CodeHighlighter.highlightStyle),K,r.EditorView.clickAddsSelectionRange.of((t=>t.altKey||t.ctrlKey)),C.instance(),I.instance(),$.instance(),r.Prec.lowest(r.EditorView.contentAttributes.of({"aria-label":w(b.codeEditor)})),t instanceof r.Text?[]:G(t),r.tooltips({parent:nt(),tooltipSpace:Z}),r.bidiIsolates()]}const et=[r.closeBrackets(),r.keymap.of(r.closeBracketsKeymap)];let ot=null;function nt(){if(!ot){const t=r.EditorState.create({extensions:[y,J()?q:[],r.syntaxHighlighting(s.CodeHighlighter.highlightStyle),r.showTooltip.of({pos:0,create:()=>({dom:document.createElement("div")})})]}).facet(r.EditorView.styleModule),e=document.body.appendChild(document.createElement("div"));e.className="editor-tooltip-host",ot=e.attachShadow({mode:"open"}),r.StyleModule.mount(ot,t)}return ot}class rt extends r.WidgetType{text;constructor(t){super(),this.text=t}eq(t){return this.text===t.text}toDOM(){const t=document.createElement("span");return t.className="cm-completionHint",t.textContent=this.text,t}}const it=r.ViewPlugin.fromClass(class{decorations=r.Decoration.none;currentHint=null;update(t){const e=this.currentHint=this.topCompletion(t.state);!e||t.state.field(M,!1)?this.decorations=r.Decoration.none:this.decorations=r.Decoration.set([r.Decoration.widget({widget:new rt(e),side:1}).range(t.state.selection.main.head)])}topCompletion(t){const e=r.selectedCompletion(t);if(!e)return null;let{label:o,apply:n}=e;if("string"==typeof n&&(o=n,n=void 0),n||o.length>100||o.indexOf("\n")>-1||"secondary"===e.type)return null;const i=t.selection.main.head,s=t.doc.lineAt(i);if(i!==s.to)return null;const a=("'"===o[0]?/'(\\.|[^'\\])*$/:'"'===o[0]?/"(\\.|[^"\\])*$/:/#?[\w$]+$/).exec(s.text);return a&&!o.startsWith(a[0])?null:o.slice(a?a[0].length:0)}},{decorations:t=>t.decorations});var st=Object.freeze({__proto__:null,dynamicSetting:x,DynamicSetting:E,tabMovesFocus:C,conservativeCompletion:M,autocompletion:P,bracketMatching:I,codeFolding:_,autoDetectIndent:A,showWhitespace:W,allowScrollPastEof:F,indentUnit:$,domWordWrap:U,dummyDarkTheme:q,themeSelection:Q,theme:X,baseConfiguration:tt,closeBrackets:et,showCompletionHint:it,contentIncludingHint:function(t){const e=t.plugin(it);let o=t.state.doc.toString();if(e&&e.currentHint){const{head:n}=t.state.selection.main;o=o.slice(0,n)+e.currentHint+o.slice(n)}return o}});const at=r.StateEffect.define();class ct{completions;seen;constructor(t=[],e=new Set){this.completions=t,this.seen=e}add(t){this.seen.has(t.label)||(this.seen.add(t.label),this.completions.push(t))}copy(){return new ct(this.completions.slice(),new Set(this.seen))}}const lt=["async","await","break","case","catch","class","const","continue","debugger","default","delete","do","else","export","extends","false","finally","for","function","if","import","in","instanceof","let","new","null","of","return","static","super","switch","this","throw","true","try","typeof","var","void","while","with","yield"],dt=["clear","copy","debug","dir","dirxml","getEventListeners","inspect","keys","monitor","monitorEvents","profile","profileEnd","queryObjects","table","undebug","unmonitor","unmonitorEvents","values"],ut=["$","$$","$x","$0","$_"],mt=new Set,pt=new ct,ht=new ct;for(const t of lt)ht.add({label:t,type:"keyword"}),pt.add({label:t,type:"keyword"});for(const t of dt)ht.add({label:t,type:"function"}),mt.has(t)&&pt.add({label:t,type:"function"});for(const t of ut)ht.add({label:t,type:"variable"}),mt.has(t)&&pt.add({label:t,type:"variable"});const ft=new Set(["TemplateString","LineComment","BlockComment","TypeDefinition","VariableDefinition","PropertyDefinition","TypeName"]);function gt(t,e,o){let n=t.resolveInner(e,-1);const r=n.parent;if(ft.has(n.name))return null;if("PropertyName"===n.name||"PrivatePropertyName"===n.name)return"MemberExpression"!==r?.name?null:{type:1,from:n.from,relatedNode:r};if("VariableName"===n.name||!n.firstChild&&n.to-n.from<20&&!/[^a-z]/.test(o.sliceString(n.from,n.to)))return{type:0,from:n.from};if("String"===n.name){const t=n.parent;return"MemberExpression"===t?.name&&"["===t.childBefore(n.from)?.name?{type:2,from:n.from,relatedNode:t}:null}if(n=n.enterUnfinishedNodesBefore(e),n.to===e&&"MemberExpression"===n.parent?.name&&(n=n.parent),"MemberExpression"===n.name){const t=n.childBefore(Math.min(e,n.to));if("["===t?.name)return{type:2,relatedNode:n};if("."===t?.name||"?."===t?.name)return{type:1,relatedNode:n}}if("("===n.name&&"ArgList"===r?.name&&"CallExpression"===r?.parent?.name){const t=r?.parent?.firstChild;if("MemberExpression"===t?.name){const e=t?.lastChild;if(e&&"get"===o.sliceString(e.from,e.to)){const e=t?.firstChild;return{type:3,relatedNode:e||void 0}}}}return{type:0}}async function yt(t){const e=gt(r.syntaxTree(t.state),t.pos,t.state.doc);if(!e||void 0===e.from&&!t.explicit&&0===e.type)return null;const o=St()?.debuggerModel.selectedCallFrame()?.script;if(o&&d.DebuggerWorkspaceBinding.DebuggerWorkspaceBinding.instance().pluginManager.hasPluginForScript(o))return null;let n,i;if(0===e.type){const[t,e]=await Promise.all([Tt(),Dt()]);if(t.completions.length){n=t;for(const t of e.completions)n.add(t)}else n=e}else if(1===e.type||2===e.type){const o=e.relatedNode.getChild("Expression");if(2===e.type&&(i=void 0===e.from?"'":t.state.sliceDoc(e.from,e.from+1)),!o)return null;n=await async function(t,e,o=!1){const n=kt.instance();if(!e){const e=n.get(t);if(e)return e}const r=St();if(!r)return new ct;const i=Mt(t,r,e,o);e||n.set(t,i);return i}(t.state.sliceDoc(o.from,o.to),i,"]"===t.state.sliceDoc(t.pos,t.pos+1))}else{if(3!==e.type)return null;{const o=e.relatedNode;if(!o)return null;n=await async function(t){const e=new ct,o=St();if(!o)return e;const n=await xt(o,`[...Map.prototype.keys.call(${t})]`,"completion");if(!n)return e;const r=l.RemoteObject.RemoteArray.objectAsArray(n),i=r.length();for(let t=0;t<i;t++)e.add({label:`"${(await r.at(t)).value}")`,type:"constant",boost:-1*t});return e}(t.state.sliceDoc(o.from,o.to))}}return{from:e.from??t.pos,options:n.completions,validFor:i?"'"===i?vt:wt:bt}}const bt=/^#?(?:[$_\p{ID_Start}])(?:[$_\u200C\u200D\p{ID_Continue}])*$/u,vt=/^\'(\\.|[^\\'\n])*'?$/,wt=/^"(\\.|[^\\"\n])*"?$/;function St(){return i.Context.Context.instance().flavor(l.RuntimeModel.ExecutionContext)}async function xt(t,e,o){const n=await t.evaluate({expression:e,objectGroup:o,includeCommandLineAPI:!0,silent:!0,returnByValue:!1,generatePreview:!1,throwOnSideEffect:!0,timeout:500,replMode:!0},!1,!1);return"error"in n||n.exceptionDetails||!n.object?null:n.object}const Et=new Map([["string","String"],["symbol","Symbol"],["number","Number"],["boolean","Boolean"],["bigint","BigInt"]]);let Ct=null;class kt{#c=new Map;constructor(){const t=()=>this.#c.clear();l.TargetManager.TargetManager.instance().addModelListener(l.ConsoleModel.ConsoleModel,l.ConsoleModel.Events.CommandEvaluated,t),i.Context.Context.instance().addFlavorChangeListener(l.RuntimeModel.ExecutionContext,t),l.TargetManager.TargetManager.instance().addModelListener(l.DebuggerModel.DebuggerModel,l.DebuggerModel.Events.DebuggerResumed,t),l.TargetManager.TargetManager.instance().addModelListener(l.DebuggerModel.DebuggerModel,l.DebuggerModel.Events.DebuggerPaused,t)}get(t){return this.#c.get(t)}set(t,e){this.#c.set(t,e),window.setTimeout((()=>{this.#c.get(t)===e&&this.#c.delete(t)}),3e4)}static instance(){return Ct||(Ct=new kt),Ct}}async function Mt(t,e,o,n=!1){const r=new ct;if(!e)return r;let i=await xt(e,t,"completion");if(!i)return r;for(;"object"===i.type&&"proxy"===i.subtype;){const t=await i.getOwnProperties(!1),e=t.internalProperties?.find((t=>"[[Target]]"===t.name))?.value;if(!e)break;i=e}const s=Et.get(i.type);s&&(i=await xt(e,s+".prototype","completion"));const a="globalThis"===t?"function":"method",c="globalThis"===t?"variable":"property";if(i&&("object"===i.type||"function"===i.type)){const t=await i.getAllProperties(!1,!1,!0),e="function"===i.type;for(const i of t.properties||[])if(!i.symbol&&(!e||"arguments"!==i.name&&"caller"!==i.name)&&(o||bt.test(i.name))){const t=o?o+i.name.replaceAll("\\","\\\\").replaceAll(o,"\\"+o)+o:i.name,e=o&&!n?`${t}]`:void 0,s=2*Number(i.isOwn)+1*Number(i.enumerable),l="function"===i.value?.type?a:c;r.add({apply:e,label:t,type:l,boost:s})}}return e.runtimeModel.releaseObjectGroup("completion"),r}async function Tt(){const t=new ct,e=St()?.debuggerModel.selectedCallFrame();if(!e)return t;const o=await Promise.all(e.scopeChain().map((t=>(t=>c.Runtime.experiments.isEnabled("evaluate-expressions-with-source-maps")?m.NamesResolver.resolveScopeInObject(t):t.object())(t).getAllProperties(!1,!1))));for(const e of o)for(const o of e.properties||[])t.add({label:o.name,type:"function"===o.value?.type?"function":"variable"});return t}async function Dt(){const t=kt.instance(),e=t.get("");if(e)return e;const o=c.Runtime.experiments.isEnabled("react-native-specific-ui")?pt:ht,n=St();if(!n)return o;const r=o.copy(),i=Mt("globalThis",n).then((t=>n.globalLexicalScopeNames().then((e=>{for(const e of t.completions)r.add(e);for(const t of e||[])r.add({label:t,type:"variable"});return r}))));return t.set("",i),i}async function jt(t,e){const o=r.syntaxTree(t).resolveInner(e).enterUnfinishedNodesBefore(e);if("ArgList"!==o.name)return null;const n=o.parent?.getChild("Expression");if(!n)return null;const i=await async function(t,e){const o=St();if(!o)return null;const n=e.sliceString(t.from,t.to),r=await xt(o,n,"argumentsHint");if(!r||"function"!==r.type)return null;const i=async()=>{const n=t.firstChild;return n&&"MemberExpression"===t.name?xt(o,e.sliceString(n.from,n.to),"argumentsHint"):null};return Ot(r,i,n).finally((()=>o.runtimeModel.releaseObjectGroup("argumentsHint")))}(n,t.doc);if(!i)return null;let s=0;for(let t=e;;){const e=o.childBefore(t);if(!e)break;e.type.is("Expression")&&s++,t=e.from}return()=>function(t,e){const o=document.createElement("div");o.className="cm-argumentHints";for(const n of t){const t=document.createElement("span");for(let o=0;o<n.length;o++){if(o===e||o<e&&n[o].startsWith("...")){t.appendChild(document.createElement("b")).appendChild(document.createTextNode(n[o]))}else t.appendChild(document.createTextNode(n[o]));o<n.length-1&&t.appendChild(document.createTextNode(", "))}const r=o.appendChild(document.createElement("div"));r.className="source-code",r.appendChild(document.createTextNode("ƒ(")),r.appendChild(t),r.appendChild(document.createTextNode(")"))}return{dom:o}}(i,s)}function Lt(t){function e(e){for(;"ParamList"!==e.name&&e.nextSibling(););const o=[];if("ParamList"===e.name&&e.firstChild()){let n="";do{switch(e.name){case"ArrayPattern":o.push(n+"arr"),n="";break;case"ObjectPattern":o.push(n+"obj"),n="";break;case"VariableDefinition":o.push(n+t.slice(e.from,e.to)),n="";break;case"Spread":n="..."}}while(e.nextSibling())}return o}try{try{const{parser:o}=r.javascript.javascriptLanguage.configure({strict:!0,top:"SingleClassItem"}),n=o.parse(t).cursor();if(n.firstChild()&&"MethodDeclaration"===n.name&&n.firstChild())return e(n);throw new Error("SingleClassItem rule is expected to have exactly one MethodDeclaration child")}catch{const{parser:o}=r.javascript.javascriptLanguage.configure({strict:!0,top:"SingleExpression"}),n=o.parse(t).cursor();if(!n.firstChild())throw new Error("SingleExpression rule is expected to have children");switch(n.name){case"ArrowFunction":case"FunctionExpression":if(!n.firstChild())throw new Error(`${n.name} rule is expected to have children`);return e(n);case"ClassExpression":if(!n.firstChild())throw new Error(`${n.name} rule is expected to have children`);for(;n.nextSibling()&&"ClassBody"!==n.name;);if("ClassBody"===n.name&&n.firstChild())do{if("MethodDeclaration"===n.name&&n.firstChild()){if("PropertyDefinition"===n.name&&"constructor"===t.slice(n.from,n.to))return e(n);n.parent()}}while(n.nextSibling());return[]}throw new Error("Unexpected expression")}}catch(e){throw new Error(`Failed to parse for arguments list: ${t}`,{cause:e})}}async function Ot(t,e,o){const n=t.description;if(!n)return null;if(!n.endsWith("{ [native code] }"))return[Lt(n)];if("function () { [native code] }"===n){const e=await async function(t){const{internalProperties:e}=await t.getOwnProperties(!1);if(!e)return null;const o=e.find((t=>"[[TargetFunction]]"===t.name))?.value,n=e.find((t=>"[[BoundArgs]]"===t.name))?.value,r=e.find((t=>"[[BoundThis]]"===t.name))?.value;if(!r||!o||!n)return null;const i=await Ot(o,(()=>Promise.resolve(r))),s=l.RemoteObject.RemoteObject.arrayLength(n);if(!i)return null;return i.map((t=>{const e=t.findIndex((t=>t.startsWith("...")));return e>-1&&e<s?t.slice(e):t.slice(s)}))}(t);if(e)return e}const r=u.JavaScriptMetadata.JavaScriptMetadataImpl.instance(),i=/^function ([^(]*)\(/.exec(n),s=i&&i[1]||o;if(!s)return null;const a=r.signaturesForNativeFunction(s);if(a)return a;const c=await e();if(!c)return null;const d=c.className;if(d){const t=r.signaturesForInstanceMethod(s,d);if(t)return t}if(c.description&&"function"===c.type&&c.description.endsWith("{ [native code] }")){const t=/^function ([^(]*)\(/.exec(c.description);if(t){const e=t[1],o=r.signaturesForStaticMethod(s,e);if(o)return o}}for(const t of await async function(t){if("number"===t.type)return["Number","Object"];if("string"===t.type)return["String","Object"];if("symbol"===t.type)return["Symbol","Object"];if("bigint"===t.type)return["BigInt","Object"];if("boolean"===t.type)return["Boolean","Object"];if("undefined"===t.type||"null"===t.subtype)return[];return await t.callFunctionJSON((function(){const t=[];for(let e=this;e;e=Object.getPrototypeOf(e))"object"==typeof e&&e.constructor&&e.constructor.name&&(t[t.length]=e.constructor.name);return t}),[])}(c)){const e=r.signaturesForInstanceMethod(s,t);if(e)return e}return null}var Pt=Object.freeze({__proto__:null,completion:function(){return r.javascript.javascriptLanguage.data.of({autocomplete:yt})},completeInContext:async function(t,e,o=!1){const n=r.EditorState.create({doc:t+e,selection:{anchor:t.length},extensions:r.javascript.javascriptLanguage}),i=await yt(new r.CompletionContext(n,n.doc.length,o));return i?i.options.filter((t=>t.label.startsWith(e))).map((t=>({text:t.label,priority:100+(t.boost||0),isSecondary:"secondary"===t.type}))):[]},getQueryType:gt,javascriptCompletionSource:yt,isExpressionComplete:async function(t){const e=i.Context.Context.instance().flavor(l.RuntimeModel.ExecutionContext);if(!e)return!0;const o=await e.runtimeModel.compileScript(t,"",!1,e.id);if(!o||!o.exceptionDetails||!o.exceptionDetails.exception)return!0;const n=o.exceptionDetails.exception.description;return!!n&&(!n.startsWith("SyntaxError: Unexpected end of input")&&!n.startsWith("SyntaxError: Unterminated template literal"))},argumentHints:function(){return function(t){const e=r.StateEffect.define();return[r.StateField.define({create:()=>null,update(t,o){if(o.selection&&(t=null),t&&!o.changes.empty){const e=o.changes.mapPos(t.pos,-1,r.MapMode.TrackDel);t=null===e?null:{pos:e,create:t.create,above:!0}}for(const n of o.effects)n.is(e)?t={pos:o.state.selection.main.from,create:n.value,above:!0}:n.is(at)&&(t=null);return t},provide:t=>r.showTooltip.from(t)}),r.ViewPlugin.fromClass(class{pending=-1;updateID=0;update(t){this.updateID++,t.transactions.some((t=>t.selection))&&t.state.selection.main.empty&&this.#l(t.view)}#l(t){this.pending>-1&&clearTimeout(this.pending),this.pending=window.setTimeout((()=>this.#d(t)),50)}#d(o){this.pending=-1;const{main:n}=o.state.selection;if(n.empty){const{updateID:r}=this;t(o.state,n.from).then((t=>{this.updateID!==r?this.pending<0&&this.#l(o):t?o.dispatch({effects:e.of(t)}):o.dispatch({effects:at.of(null)})}))}}})]}(jt)},closeArgumentsHintsTooltip:function(t,e){return null!==t.state.field(e)&&(t.dispatch({effects:at.of(null)}),!0)},argumentsList:Lt});function It(t,{lineNumber:e,columnNumber:o}){const n=t.line(Math.max(1,Math.min(t.lines,e+1)));return Math.max(n.from,Math.min(n.to,n.from+o))}function _t(t,e){e=Math.max(0,Math.min(e,t.length));const o=t.lineAt(e);return{lineNumber:o.number-1,columnNumber:e-o.from}}var Nt=Object.freeze({__proto__:null,toOffset:It,toLineColumn:_t});class At extends HTMLElement{static litTagName=h.literal`devtools-text-editor`;#u=this.attachShadow({mode:"open"});#m=void 0;#p=E.none;#h=[];#f;#g;#y=-1;#b=()=>{this.#y<0&&(this.#y=window.setTimeout((()=>{this.#y=-1,this.#m&&r.repositionTooltips(this.#m)}),50))};#v=new ResizeObserver(this.#b);constructor(t){super(),this.#f=t,this.#u.adoptedStyleSheets=[s.Style.default]}#w(){return this.#m=new r.EditorView({state:this.state,parent:this.#u,root:this.#u,dispatch:(t,e)=>{e.update([t]),t.reconfigured&&this.#S()},scrollTo:this.#g}),this.#m.scrollDOM.addEventListener("scroll",(()=>{this.#m&&(this.#g=this.#m.scrollSnapshot(),this.scrollEventHandledToSaveScrollPositionForTest())})),this.#S(),this.#x(),p.ThemeSupport.instance().addEventListener(p.ThemeChangeEvent.eventName,(()=>{const t="dark"===p.ThemeSupport.instance().themeName()?q:[];this.editor.dispatch({effects:Q.reconfigure(t)})})),this.#m}get editor(){return this.#m||this.#w()}dispatch(t){return this.editor.dispatch(t)}get state(){return this.#m?this.#m.state:(this.#f||(this.#f=r.EditorState.create({extensions:tt("")})),this.#f)}set state(t){this.#f!==t&&(this.#f=t,this.#m&&(this.#m.setState(t),this.#S()))}scrollEventHandledToSaveScrollPositionForTest(){}connectedCallback(){this.#m?this.#m.dispatch({effects:this.#g}):this.#w()}disconnectedCallback(){this.#m&&(this.#m.dispatch({effects:Ht.of(null)}),this.#f=this.#m.state,this.#v.disconnect(),window.removeEventListener("resize",this.#b),this.#m.destroy(),this.#m=void 0,this.#S())}focus(){this.#m&&this.#m.focus()}#S(){const e=this.#m?this.#m.state.facet(x):E.none;if(e===this.#p)return;this.#p=e;for(const[t,e]of this.#h)t.removeChangeListener(e);this.#h=[];const o=t.Settings.Settings.instance();for(const t of e){const e=({data:e})=>{const o=t.sync(this.state,e);o&&this.#m&&this.#m.dispatch({effects:o})},n=o.moduleSetting(t.settingName);n.addChangeListener(e),this.#h.push([n,e])}}#x(){const t=n.WindowBoundsService.WindowBoundsServiceImpl.instance().getDevToolsBoundingElement();t&&this.#v.observe(t),window.addEventListener("resize",this.#b)}revealPosition(t,e=!0){const o=this.#m;if(!o)return;const n=o.state.doc.lineAt(t.main.head),i=[];e&&(o.state.field(Rt,!1)?o.dispatch({effects:Ht.of(null)}):o.dispatch({effects:r.StateEffect.appendConfig.of(Rt)}),i.push(Bt.of(n.from)));const s=o.scrollDOM.getBoundingClientRect(),a=o.coordsAtPos(t.main.head);t.main.empty?!a||a.top<s.top||a.bottom>s.bottom?i.push(r.EditorView.scrollIntoView(t.main,{y:"center"})):(a.left<s.left||a.right>s.right)&&i.push(r.EditorView.scrollIntoView(t.main,{x:"center"})):i.push(r.EditorView.scrollIntoView(t.main)),o.dispatch({selection:t,effects:i,userEvent:"select.reveal"})}createSelection(t,e){const{doc:o}=this.state,n=It(o,t);return r.EditorSelection.single(e?It(o,e):n,n)}toLineColumn(t){return _t(this.state.doc,t)}toOffset(t){return It(this.state.doc,t)}}customElements.define("devtools-text-editor",At);const Ht=r.StateEffect.define(),Bt=r.StateEffect.define(),Rt=r.StateField.define({create:()=>r.Decoration.none,update(t,e){!e.changes.empty&&t.size&&(t=t.map(e.changes));for(const o of e.effects)o.is(Ht)?t=r.Decoration.none:o.is(Bt)&&(t=r.Decoration.set([r.Decoration.line({attributes:{class:"cm-highlightedLine"}}).range(o.value)]));return t},provide:t=>r.EditorView.decorations.from(t,(t=>t))});var zt=Object.freeze({__proto__:null,TextEditor:At});var Wt=Object.freeze({__proto__:null,TextEditorHistory:class{#E;#C;constructor(t,e){this.#E=t,this.#C=e}moveHistory(t,e=!1){const{editor:o}=this.#E,{main:n}=o.state.selection,i=-1===t;if(!e){if(!n.empty)return!1;const t=o.coordsAtPos(n.head),e=o.coordsAtPos(i?0:o.state.doc.length);if(t&&e&&(i?t.top>e.top+5:t.bottom<e.bottom-5))return!1}const s=o.state.doc.toString(),a=this.#C,c=i?a.previous(s):a.next();if(void 0===c)return!1;const l=c.length;if(o.dispatch({changes:{from:0,to:o.state.doc.length,insert:c},selection:r.EditorSelection.cursor(l),scrollIntoView:!0}),i){const t=c.search(/\n|$/);o.dispatch({selection:r.EditorSelection.cursor(t)})}return!0}historyCompletions(t){const{explicit:e,pos:o,state:n}=t,r=n.doc.toString();if(!(o===r.length)||!r.length&&!e)return null;const i=this.#C.matchingEntries(r);if(!i.size)return null;const s=[...i].map((t=>({label:t,type:"secondary",boost:-1e5})));return{from:0,to:r.length,options:s}}}});export{g as AutocompleteHistory,st as Config,Pt as JavaScript,Nt as Position,zt as TextEditor,Wt as TextEditorHistory};
