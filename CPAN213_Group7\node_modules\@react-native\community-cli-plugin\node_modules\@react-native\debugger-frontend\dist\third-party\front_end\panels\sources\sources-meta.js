import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as o from"../../core/i18n/i18n.js";import*as i from"../../core/root/root.js";import*as a from"../../core/sdk/sdk.js";import*as n from"../../models/breakpoints/breakpoints.js";import*as s from"../../models/workspace/workspace.js";import*as r from"../../ui/legacy/components/object_ui/object_ui.js";import*as c from"../../ui/legacy/components/quick_open/quick_open.js";import*as l from"../../ui/legacy/legacy.js";const d={showSources:"Show Sources",sources:"Sources",showWorkspace:"Show Workspace",workspace:"Workspace",showSnippets:"Show Snippets",snippets:"Snippets",showSearch:"Show Search",search:"Search",showQuickSource:"Show Quick source",quickSource:"Quick source",showThreads:"Show Threads",threads:"Threads",showScope:"Show Scope",scope:"Scope",showWatch:"Show Watch",watch:"Watch",showBreakpoints:"Show Breakpoints",breakpoints:"Breakpoints",pauseScriptExecution:"Pause script execution",resumeScriptExecution:"Resume script execution",stepOverNextFunctionCall:"Step over next function call",stepIntoNextFunctionCall:"Step into next function call",step:"Step",stepOutOfCurrentFunction:"Step out of current function",runSnippet:"Run snippet",deactivateBreakpoints:"Deactivate breakpoints",activateBreakpoints:"Activate breakpoints",addSelectedTextToWatches:"Add selected text to watches",evaluateSelectedTextInConsole:"Evaluate selected text in console",switchFile:"Switch file",rename:"Rename",closeAll:"Close All",jumpToPreviousEditingLocation:"Jump to previous editing location",jumpToNextEditingLocation:"Jump to next editing location",closeTheActiveTab:"Close the active tab",goToLine:"Go to line",goToAFunctionDeclarationruleSet:"Go to a function declaration/rule set",toggleBreakpoint:"Toggle breakpoint",toggleBreakpointEnabled:"Toggle breakpoint enabled",toggleBreakpointInputWindow:"Toggle breakpoint input window",save:"Save",saveAll:"Save all",createNewSnippet:"Create new snippet",addFolderToWorkspace:"Add folder to workspace",addFolder:"Add folder",previousCallFrame:"Previous call frame",nextCallFrame:"Next call frame",incrementCssUnitBy:"Increment CSS unit by {PH1}",decrementCssUnitBy:"Decrement CSS unit by {PH1}",searchInAnonymousAndContent:"Search in anonymous and content scripts",doNotSearchInAnonymousAndContent:"Do not search in anonymous and content scripts",automaticallyRevealFilesIn:"Automatically reveal files in sidebar",doNotAutomaticallyRevealFilesIn:"Do not automatically reveal files in sidebar",javaScriptSourceMaps:"JavaScript source maps",enableJavaScriptSourceMaps:"Enable JavaScript source maps",disableJavaScriptSourceMaps:"Disable JavaScript source maps",tabMovesFocus:"Tab moves focus",enableTabMovesFocus:"Enable tab moves focus",disableTabMovesFocus:"Disable tab moves focus",detectIndentation:"Detect indentation",doNotDetectIndentation:"Do not detect indentation",autocompletion:"Autocompletion",enableAutocompletion:"Enable autocompletion",disableAutocompletion:"Disable autocompletion",bracketMatching:"Bracket matching",enableBracketMatching:"Enable bracket matching",disableBracketMatching:"Disable bracket matching",codeFolding:"Code folding",enableCodeFolding:"Enable code folding",disableCodeFolding:"Disable code folding",showWhitespaceCharacters:"Show whitespace characters:",doNotShowWhitespaceCharacters:"Do not show whitespace characters",none:"None",showAllWhitespaceCharacters:"Show all whitespace characters",all:"All",showTrailingWhitespaceCharacters:"Show trailing whitespace characters",trailing:"Trailing",displayVariableValuesInlineWhile:"Display variable values inline while debugging",doNotDisplayVariableValuesInline:"Do not display variable values inline while debugging",cssSourceMaps:"CSS source maps",enableCssSourceMaps:"Enable CSS source maps",disableCssSourceMaps:"Disable CSS source maps",allowScrollingPastEndOfFile:"Allow scrolling past end of file",disallowScrollingPastEndOfFile:"Disallow scrolling past end of file",wasmAutoStepping:"When debugging wasm with debug information, do not pause on wasm bytecode if possible",enableWasmAutoStepping:"Enable wasm auto-stepping",disableWasmAutoStepping:"Disable wasm auto-stepping",goTo:"Go to",line:"Line",symbol:"Symbol",open:"Open",file:"File",disableAutoFocusOnDebuggerPaused:"Do not focus Sources panel when triggering a breakpoint",enableAutoFocusOnDebuggerPaused:"Focus Sources panel when triggering a breakpoint",revealActiveFileInSidebar:"Reveal active file in navigator sidebar",toggleNavigatorSidebar:"Toggle navigator sidebar",toggleDebuggerSidebar:"Toggle debugger sidebar",nextEditorTab:"Next editor",previousEditorTab:"Previous editor"},g=o.i18n.registerUIStrings("panels/sources/sources-meta.ts",d),u=o.i18n.getLazilyComputedLocalizedString.bind(void 0,g);let p,S;async function w(){return p||(p=await import("./sources.js")),p}async function b(){return S||(S=await import("./components/components.js")),S}function m(e){return void 0===p?[]:e(p)}l.ViewManager.registerViewExtension({location:"panel",id:"sources",commandPrompt:u(d.showSources),title:u(d.sources),order:30,loadView:async()=>(await w()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-files",commandPrompt:u(d.showWorkspace),title:u(d.workspace),order:3,persistence:"permanent",loadView:async()=>new((await w()).SourcesNavigator.FilesNavigatorView),condition:i.Runtime.conditions.notSourcesHideAddFolder}),l.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-snippets",commandPrompt:u(d.showSnippets),title:u(d.snippets),order:6,persistence:"permanent",loadView:async()=>new((await w()).SourcesNavigator.SnippetsNavigatorView)}),l.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.search-sources-tab",commandPrompt:u(d.showSearch),title:u(d.search),order:7,persistence:"closeable",loadView:async()=>new((await w()).SearchSourcesView.SearchSourcesView)}),l.ViewManager.registerViewExtension({location:"drawer-view",id:"sources.quick",commandPrompt:u(d.showQuickSource),title:u(d.quickSource),persistence:"closeable",order:1e3,loadView:async()=>new((await w()).SourcesPanel.QuickSourceView)}),l.ViewManager.registerViewExtension({id:"sources.threads",commandPrompt:u(d.showThreads),title:u(d.threads),persistence:"permanent",loadView:async()=>new((await w()).ThreadsSidebarPane.ThreadsSidebarPane)}),l.ViewManager.registerViewExtension({id:"sources.scope-chain",commandPrompt:u(d.showScope),title:u(d.scope),persistence:"permanent",loadView:async()=>(await w()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),l.ViewManager.registerViewExtension({id:"sources.watch",commandPrompt:u(d.showWatch),title:u(d.watch),persistence:"permanent",loadView:async()=>(await w()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),hasToolbar:!0}),l.ViewManager.registerViewExtension({id:"sources.js-breakpoints",commandPrompt:u(d.showBreakpoints),title:u(d.breakpoints),persistence:"permanent",loadView:async()=>(await b()).BreakpointsView.BreakpointsView.instance().wrapper}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.toggle-pause",iconClass:"pause",toggleable:!0,toggledIconClass:"resume",loadActionDelegate:async()=>new((await w()).SourcesPanel.RevealingActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView,l.ShortcutRegistry.ForwardedShortcut])),options:[{value:!0,title:u(d.pauseScriptExecution)},{value:!1,title:u(d.resumeScriptExecution)}],bindings:[{shortcut:"F8",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+\\"},{shortcut:"F5",keybindSets:["vsCode"]},{shortcut:"Shift+F5",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+\\"}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-over",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.stepOverNextFunctionCall),iconClass:"step-over",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F10",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+'"},{platform:"mac",shortcut:"Meta+'"}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-into",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.stepIntoNextFunctionCall),iconClass:"step-into",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+;"},{platform:"mac",shortcut:"Meta+;"}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.step),iconClass:"step",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"F9",keybindSets:["devToolsDefault"]}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.step-out",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.stepOutOfCurrentFunction),iconClass:"step-out",contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Shift+F11",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Shift+Ctrl+;"},{platform:"mac",shortcut:"Shift+Meta+;"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.run-snippet",category:"DEBUGGER",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.runSnippet),iconClass:"play",contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Enter"},{platform:"mac",shortcut:"Meta+Enter"}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.toggle-breakpoints-active",iconClass:"breakpoint-crossed",toggledIconClass:"breakpoint-crossed-filled",toggleable:!0,loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),options:[{value:!0,title:u(d.deactivateBreakpoints)},{value:!1,title:u(d.activateBreakpoints)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+F8"},{platform:"mac",shortcut:"Meta+F8"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.add-to-watch",loadActionDelegate:async()=>(await w()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),category:"DEBUGGER",title:u(d.addSelectedTextToWatches),contextTypes:()=>m((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+A"},{platform:"mac",shortcut:"Meta+Shift+A"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.evaluate-selection",category:"DEBUGGER",loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),title:u(d.evaluateSelectedTextInConsole),contextTypes:()=>m((e=>[e.UISourceCodeFrame.UISourceCodeFrame])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.switch-file",category:"SOURCES",title:u(d.switchFile),loadActionDelegate:async()=>new((await w()).SourcesView.SwitchFileActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+O"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.rename",category:"SOURCES",title:u(d.rename),bindings:[{platform:"windows,linux",shortcut:"F2"},{platform:"mac",shortcut:"Enter"}]}),l.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.close-all",loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),title:u(d.closeAll)}),l.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-previous-location",category:"SOURCES",title:u(d.jumpToPreviousEditingLocation),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Minus"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.jump-to-next-location",category:"SOURCES",title:u(d.jumpToNextEditingLocation),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+Plus"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.close-editor-tab",category:"SOURCES",title:u(d.closeTheActiveTab),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Alt+w"},{shortcut:"Ctrl+W",keybindSets:["vsCode"]},{platform:"windows",shortcut:"Ctrl+F4",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.next-editor-tab",category:"SOURCES",title:u(d.nextEditorTab),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageDown",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageDown",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.previous-editor-tab",category:"SOURCES",title:u(d.previousEditorTab),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+PageUp",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+PageUp",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.go-to-line",category:"SOURCES",title:u(d.goToLine),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{shortcut:"Ctrl+g",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.go-to-member",category:"SOURCES",title:u(d.goToAFunctionDeclarationruleSet),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+Shift+o",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+T",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+T",keybindSets:["vsCode"]},{shortcut:"F12",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint",category:"DEBUGGER",title:u(d.toggleBreakpoint),bindings:[{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+b",keybindSets:["devToolsDefault"]},{shortcut:"F9",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.toggle-breakpoint-enabled",category:"DEBUGGER",title:u(d.toggleBreakpointEnabled),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+b"},{platform:"mac",shortcut:"Meta+Shift+b"}]}),l.ActionRegistration.registerActionExtension({actionId:"debugger.breakpoint-input-window",category:"DEBUGGER",title:u(d.toggleBreakpointInputWindow),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Alt+b"},{platform:"mac",shortcut:"Meta+Alt+b"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.save",category:"SOURCES",title:u(d.save),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+s",keybindSets:["devToolsDefault","vsCode"]},{platform:"mac",shortcut:"Meta+s",keybindSets:["devToolsDefault","vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.save-all",category:"SOURCES",title:u(d.saveAll),loadActionDelegate:async()=>new((await w()).SourcesView.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+s"},{platform:"mac",shortcut:"Meta+Alt+s"},{platform:"windows,linux",shortcut:"Ctrl+K S",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Alt+S",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.create-snippet",loadActionDelegate:async()=>new((await w()).SourcesNavigator.ActionDelegate),title:u(d.createNewSnippet)}),t.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()||l.ActionRegistration.registerActionExtension({category:"SOURCES",actionId:"sources.add-folder-to-workspace",loadActionDelegate:async()=>new((await w()).SourcesNavigator.ActionDelegate),iconClass:"plus",title:u(d.addFolderToWorkspace)}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.previous-call-frame",loadActionDelegate:async()=>new((await w()).CallStackSidebarPane.ActionDelegate),title:u(d.previousCallFrame),contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+,"}]}),l.ActionRegistration.registerActionExtension({category:"DEBUGGER",actionId:"debugger.next-call-frame",loadActionDelegate:async()=>new((await w()).CallStackSidebarPane.ActionDelegate),title:u(d.nextCallFrame),contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],bindings:[{shortcut:"Ctrl+."}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.search",title:u(d.search),loadActionDelegate:async()=>new((await w()).SearchSourcesView.ActionDelegate),category:"SOURCES",bindings:[{platform:"mac",shortcut:"Meta+Alt+F",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+F",keybindSets:["devToolsDefault","vsCode"]},{platform:"windows,linux",shortcut:"Ctrl+Shift+J",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+F",keybindSets:["vsCode"]},{platform:"mac",shortcut:"Meta+Shift+J",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.increment-css",category:"SOURCES",title:u(d.incrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Up"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.increment-css-by-ten",title:u(d.incrementCssUnitBy,{PH1:10}),category:"SOURCES",bindings:[{shortcut:"Alt+PageUp"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css",category:"SOURCES",title:u(d.decrementCssUnitBy,{PH1:1}),bindings:[{shortcut:"Alt+Down"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.decrement-css-by-ten",category:"SOURCES",title:u(d.decrementCssUnitBy,{PH1:10}),bindings:[{shortcut:"Alt+PageDown"}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.reveal-in-navigator-sidebar",category:"SOURCES",title:u(d.revealActiveFileInSidebar),loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView]))}),l.ActionRegistration.registerActionExtension({actionId:"sources.toggle-navigator-sidebar",category:"SOURCES",title:u(d.toggleNavigatorSidebar),loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+y",keybindSets:["devToolsDefault"]},{platform:"mac",shortcut:"Meta+Shift+y",keybindSets:["devToolsDefault"]},{platform:"windows,linux",shortcut:"Ctrl+b",keybindSets:["vsCode"]},{platform:"windows,linux",shortcut:"Meta+b",keybindSets:["vsCode"]}]}),l.ActionRegistration.registerActionExtension({actionId:"sources.toggle-debugger-sidebar",category:"SOURCES",title:u(d.toggleDebuggerSidebar),loadActionDelegate:async()=>new((await w()).SourcesPanel.ActionDelegate),contextTypes:()=>m((e=>[e.SourcesView.SourcesView])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+h"},{platform:"mac",shortcut:"Meta+Shift+h"}]}),e.Settings.registerSettingExtension({settingName:"navigator-group-by-folder",settingType:"boolean",defaultValue:!0}),e.Settings.registerSettingExtension({settingName:"navigator-group-by-authored",settingType:"boolean",defaultValue:!1}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.searchInAnonymousAndContent),settingName:"search-in-anonymous-and-content-scripts",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:u(d.searchInAnonymousAndContent)},{value:!1,title:u(d.doNotSearchInAnonymousAndContent)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.automaticallyRevealFilesIn),settingName:"auto-reveal-in-navigator",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.automaticallyRevealFilesIn)},{value:!1,title:u(d.doNotAutomaticallyRevealFilesIn)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.javaScriptSourceMaps),settingName:"js-source-maps-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableJavaScriptSourceMaps)},{value:!1,title:u(d.disableJavaScriptSourceMaps)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.tabMovesFocus),settingName:"text-editor-tab-moves-focus",settingType:"boolean",defaultValue:!1,options:[{value:!0,title:u(d.enableTabMovesFocus)},{value:!1,title:u(d.disableTabMovesFocus)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.detectIndentation),settingName:"text-editor-auto-detect-indent",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.detectIndentation)},{value:!1,title:u(d.doNotDetectIndentation)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.autocompletion),settingName:"text-editor-autocompletion",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableAutocompletion)},{value:!1,title:u(d.disableAutocompletion)}]}),e.Settings.registerSettingExtension({category:"SOURCES",title:u(d.bracketMatching),settingName:"text-editor-bracket-matching",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableBracketMatching)},{value:!1,title:u(d.disableBracketMatching)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.codeFolding),settingName:"text-editor-code-folding",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableCodeFolding)},{value:!1,title:u(d.disableCodeFolding)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.showWhitespaceCharacters),settingName:"show-whitespaces-in-editor",settingType:"enum",defaultValue:"original",options:[{title:u(d.doNotShowWhitespaceCharacters),text:u(d.none),value:"none"},{title:u(d.showAllWhitespaceCharacters),text:u(d.all),value:"all"},{title:u(d.showTrailingWhitespaceCharacters),text:u(d.trailing),value:"trailing"}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.displayVariableValuesInlineWhile),settingName:"inline-variable-values",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.displayVariableValuesInlineWhile)},{value:!1,title:u(d.doNotDisplayVariableValuesInline)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.enableAutoFocusOnDebuggerPaused),settingName:"auto-focus-on-debugger-paused-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableAutoFocusOnDebuggerPaused)},{value:!1,title:u(d.disableAutoFocusOnDebuggerPaused)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.cssSourceMaps),settingName:"css-source-maps-enabled",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableCssSourceMaps)},{value:!1,title:u(d.disableCssSourceMaps)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:u(d.allowScrollingPastEndOfFile),settingName:"allow-scroll-past-eof",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.allowScrollingPastEndOfFile)},{value:!1,title:u(d.disallowScrollingPastEndOfFile)}]}),e.Settings.registerSettingExtension({category:"SOURCES",storageType:"Local",title:u(d.wasmAutoStepping),settingName:"wasm-auto-stepping",settingType:"boolean",defaultValue:!0,options:[{value:!0,title:u(d.enableWasmAutoStepping)},{value:!1,title:u(d.disableWasmAutoStepping)}]}),l.ViewManager.registerLocationResolver({name:"navigator-view",category:"SOURCES",loadResolver:async()=>(await w()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-top",category:"SOURCES",loadResolver:async()=>(await w()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-bottom",category:"SOURCES",loadResolver:async()=>(await w()).SourcesPanel.SourcesPanel.instance()}),l.ViewManager.registerLocationResolver({name:"sources.sidebar-tabs",category:"SOURCES",loadResolver:async()=>(await w()).SourcesPanel.SourcesPanel.instance()}),l.ContextMenu.registerProvider({contextTypes:()=>[s.UISourceCode.UISourceCode,s.UISourceCode.UILocation,a.RemoteObject.RemoteObject,a.NetworkRequest.NetworkRequest,...m((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],loadProvider:async()=>(await w()).SourcesPanel.SourcesPanel.instance(),experiment:void 0}),l.ContextMenu.registerProvider({loadProvider:async()=>(await w()).WatchExpressionsSidebarPane.WatchExpressionsSidebarPane.instance(),contextTypes:()=>[r.ObjectPropertiesSection.ObjectPropertyTreeElement,...m((e=>[e.UISourceCodeFrame.UISourceCodeFrame]))],experiment:void 0}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).SourcesPanel.UILocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UILocationRange],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).SourcesPanel.UILocationRangeRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[a.DebuggerModel.Location],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).SourcesPanel.DebuggerLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[s.UISourceCode.UISourceCode],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).SourcesPanel.UISourceCodeRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).SourcesPanel.DebuggerPausedDetailsRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>[n.BreakpointManager.BreakpointLocation],destination:e.Revealer.RevealerDestination.SOURCES_PANEL,loadRevealer:async()=>new((await w()).DebuggerPlugin.BreakpointLocationRevealer)}),e.Revealer.registerRevealer({contextTypes:()=>m((e=>[e.SearchSourcesView.SearchSources])),destination:void 0,loadRevealer:async()=>new((await w()).SearchSourcesView.Revealer)}),l.Toolbar.registerToolbarItem({actionId:"sources.add-folder-to-workspace",location:"files-navigator-toolbar",label:u(d.addFolder),showLabel:!0,loadItem:void 0,order:void 0,separator:void 0}),l.Context.registerListener({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await b()).BreakpointsView.BreakpointsSidebarController.instance()}),l.Context.registerListener({contextTypes:()=>[a.DebuggerModel.DebuggerPausedDetails],loadListener:async()=>(await w()).CallStackSidebarPane.CallStackSidebarPane.instance()}),l.Context.registerListener({contextTypes:()=>[a.DebuggerModel.CallFrame],loadListener:async()=>(await w()).ScopeChainSidebarPane.ScopeChainSidebarPane.instance()}),l.ContextMenu.registerItem({location:"navigatorMenu/default",actionId:"quick-open.show",order:void 0}),l.ContextMenu.registerItem({location:"mainMenu/default",actionId:"sources.search",order:void 0}),c.FilteredListWidget.registerProvider({prefix:"@",iconName:"symbol",iconWidth:"20px",provider:async()=>new((await w()).OutlineQuickOpen.OutlineQuickOpen),titlePrefix:u(d.goTo),titleSuggestion:u(d.symbol)}),c.FilteredListWidget.registerProvider({prefix:":",iconName:"colon",iconWidth:"20px",provider:async()=>new((await w()).GoToLineQuickOpen.GoToLineQuickOpen),titlePrefix:u(d.goTo),titleSuggestion:u(d.line)}),c.FilteredListWidget.registerProvider({prefix:"",iconName:"document",iconWidth:"20px",provider:async()=>new((await w()).OpenFileQuickOpen.OpenFileQuickOpen),titlePrefix:u(d.open),titleSuggestion:u(d.file)});
