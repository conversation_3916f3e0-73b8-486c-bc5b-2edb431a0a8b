import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { CommonActions } from '@react-navigation/native';
import { fetchEvents, setSelectedCity, setSelectedCategory } from '../redux/eventsSlice';

const SearchScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { selectedCity, selectedCategory, loading } = useSelector(state => state.events);
  
  const [searchKeyword, setSearchKeyword] = useState('');
  const [tempCity, setTempCity] = useState(selectedCity);
  const [tempCategory, setTempCategory] = useState(selectedCategory);

  const categories = [
    { id: '', name: 'All Categories' },
    { id: 'Music', name: 'Music' },
    { id: 'Sports', name: 'Sports' },
    { id: 'Arts & Theatre', name: 'Arts & Theatre' },
    { id: 'Comedy', name: 'Comedy' },
    { id: 'Family', name: 'Family' },
    { id: 'Miscellaneous', name: 'Miscellaneous' },
  ];

  const popularCities = [
    'Toronto',
    'Vancouver',
    'Montreal',
    'Calgary',
    'Ottawa',
    'Edmonton',
    'Winnipeg',
    'Quebec City',
  ];

  const handleSearch = async () => {
    if (!tempCity.trim()) {
      Alert.alert('Error', 'Please enter a city name');
      return;
    }

    // Update Redux state
    dispatch(setSelectedCity(tempCity));
    dispatch(setSelectedCategory(tempCategory));

    // Fetch events with search parameters
    try {
      await dispatch(fetchEvents({
        city: tempCity,
        category: tempCategory,
        keyword: searchKeyword,
      }));
      
      // Navigate to Home tab to show results
      navigation.dispatch(
        CommonActions.navigate({
          name: 'Home',
          params: {},
        })
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to search events. Please try again.');
    }
  };

  const handleCitySelect = (city) => {
    setTempCity(city);
  };

  const handleCategorySelect = (categoryId) => {
    setTempCategory(categoryId);
  };

  const renderCategoryButton = (category) => {
    const isSelected = tempCategory === category.id;
    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.categoryButton,
          isSelected && styles.categoryButtonSelected
        ]}
        onPress={() => handleCategorySelect(category.id)}
      >
        <Text style={[
          styles.categoryButtonText,
          isSelected && styles.categoryButtonTextSelected
        ]}>
          {category.name}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderCityButton = (city) => {
    const isSelected = tempCity === city;
    return (
      <TouchableOpacity
        key={city}
        style={[
          styles.cityButton,
          isSelected && styles.cityButtonSelected
        ]}
        onPress={() => handleCitySelect(city)}
      >
        <Text style={[
          styles.cityButtonText,
          isSelected && styles.cityButtonTextSelected
        ]}>
          {city}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.content}>
        {/* Search by Keyword */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Search Events</Text>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search-outline" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Enter event name or keyword..."
              value={searchKeyword}
              onChangeText={setSearchKeyword}
              returnKeyType="search"
              onSubmitEditing={handleSearch}
            />
          </View>
        </View>

        {/* City Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select City</Text>
          <View style={styles.customCityContainer}>
            <TextInput
              style={styles.cityInput}
              placeholder="Enter city name..."
              value={tempCity}
              onChangeText={setTempCity}
            />
          </View>
          
          <Text style={styles.subsectionTitle}>Popular Cities</Text>
          <View style={styles.cityGrid}>
            {popularCities.map(renderCityButton)}
          </View>
        </View>

        {/* Category Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Select Category</Text>
          <View style={styles.categoryGrid}>
            {categories.map(renderCategoryButton)}
          </View>
        </View>

        {/* Search Button */}
        <TouchableOpacity
          style={[styles.searchButton, loading && styles.searchButtonDisabled]}
          onPress={handleSearch}
          disabled={loading}
        >
          <Ionicons name="search" size={20} color="#fff" />
          <Text style={styles.searchButtonText}>
            {loading ? 'Searching...' : 'Search Events'}
          </Text>
        </TouchableOpacity>

        {/* Current Selection Summary */}
        <View style={styles.summaryContainer}>
          <Text style={styles.summaryTitle}>Current Search:</Text>
          <Text style={styles.summaryText}>
            City: {tempCity || 'Not selected'}
          </Text>
          <Text style={styles.summaryText}>
            Category: {categories.find(cat => cat.id === tempCategory)?.name || 'All Categories'}
          </Text>
          {searchKeyword && (
            <Text style={styles.summaryText}>
              Keyword: {searchKeyword}
            </Text>
          )}
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  content: {
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
    marginTop: 16,
    marginBottom: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  customCityContainer: {
    marginBottom: 8,
  },
  cityInput: {
    backgroundColor: '#fff',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cityGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  cityButton: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  cityButtonSelected: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  cityButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  cityButtonTextSelected: {
    color: '#fff',
  },
  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  categoryButtonSelected: {
    backgroundColor: '#6366f1',
    borderColor: '#6366f1',
  },
  categoryButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  categoryButtonTextSelected: {
    color: '#fff',
  },
  searchButton: {
    backgroundColor: '#6366f1',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  searchButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  searchButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
});

export default SearchScreen;
