import*as e from"../../core/common/common.js";import*as n from"../../core/host/host.js";import*as t from"../components/render_coordinator/render_coordinator.js";import{assertNotNullOrUndefined as o}from"../../core/platform/platform.js";const r="jslog";function i(e){return e.hasAttribute(r)}function s(e){return l(e.getAttribute(r)||"")}var c;function l(e){const n=e.replace(/ /g,"").split(";"),t=e=>n.find((n=>n.startsWith(e)))?.substr(e.length),o=function(e){return c[e]??0}(n[0]);if(0===o)throw new Error("Unkown VE: "+e);const r={ve:o},i=t("context:");i&&(r.context=i);const s=t("parent:");s&&(r.parent=s);const l=t("track:");return l&&(r.track=new Map(l.split(",").map((e=>e.split(":"))))),r}function a(e,n){const t=[e];return void 0!==n&&t.push(`context: ${n}`),{context:function(e){return void 0!==e&&t.push(`context: ${e}`),this},parent:function(e){return t.push(`parent: ${e}`),this},track:function(e){return t.push(`track: ${Object.entries(e).map((([e,n])=>!0!==n?`${e}: ${n}`:e)).join(", ")}`),this},toString:function(){return t.join("; ")}}}!function(e){e[e.TreeItem=1]="TreeItem",e[e.Close=2]="Close",e[e.Counter=3]="Counter",e[e.Drawer=4]="Drawer",e[e.Resizer=5]="Resizer",e[e.Toggle=6]="Toggle",e[e.Tree=7]="Tree",e[e.TextField=8]="TextField",e[e.AnimationClip=9]="AnimationClip",e[e.Section=10]="Section",e[e.SectionHeader=11]="SectionHeader",e[e.Timeline=12]="Timeline",e[e.StylesSelector=13]="StylesSelector",e[e.Expand=14]="Expand",e[e.ToggleSubpane=15]="ToggleSubpane",e[e.ControlPoint=16]="ControlPoint",e[e.Toolbar=17]="Toolbar",e[e.Popover=18]="Popover",e[e.BreakpointMarker=19]="BreakpointMarker",e[e.DropDown=20]="DropDown",e[e.Adorner=21]="Adorner",e[e.MetricsBox=23]="MetricsBox",e[e.MetricsBoxPart=24]="MetricsBoxPart",e[e.DOMBreakpoint=26]="DOMBreakpoint",e[e.Action=29]="Action",e[e.FilterDropdown=30]="FilterDropdown",e[e.Dialog=31]="Dialog",e[e.BezierCurveEditor=32]="BezierCurveEditor",e[e.BezierPresetCategory=34]="BezierPresetCategory",e[e.Preview=35]="Preview",e[e.Canvas=36]="Canvas",e[e.ColorEyeDropper=37]="ColorEyeDropper",e[e.Link=44]="Link",e[e.Item=46]="Item",e[e.PaletteColorShades=47]="PaletteColorShades",e[e.Panel=48]="Panel",e[e.ShowStyleEditor=50]="ShowStyleEditor",e[e.Slider=51]="Slider",e[e.CssColorMix=52]="CssColorMix",e[e.Value=53]="Value",e[e.Key=54]="Key",e[e.PieChart=59]="PieChart",e[e.PieChartSlice=60]="PieChartSlice",e[e.PieChartTotal=61]="PieChartTotal",e[e.ElementsBreadcrumbs=62]="ElementsBreadcrumbs",e[e.PanelTabHeader=66]="PanelTabHeader",e[e.Menu=67]="Menu",e[e.TableHeader=69]="TableHeader",e[e.TableCell=70]="TableCell",e[e.Pane=72]="Pane",e[e.ResponsivePresets=73]="ResponsivePresets",e[e.DeviceModeRuler=74]="DeviceModeRuler",e[e.MediaInspectorView=75]="MediaInspectorView"}(c||(c={}));const d=new WeakMap;function u(){const e=new Uint32Array(1);return crypto.getRandomValues(e),e[0]}function p(e,n,t){if(d.has(e)){const o=d.get(e);return t&&!n.parent&&o.parent!==g(t)&&(o.parent=g(t)),o}if(n.parent&&h.has(n.parent)&&e instanceof Element)for(t=h.get(n.parent)?.(e);t instanceof Element&&!i(t);)t=t.parentElementOrShadowHost()??void 0;const o={impressionLogged:!1,processed:!1,config:n,veid:u(),parent:t?g(t):null};return d.set(e,o),o}function g(e){return d.get(e)||null}const h=new Map;function f(e,n){if(h.has(e))throw new Error(`Parent provider with the name '${e} is already registered'`);h.set(e,n)}const b=new WeakMap;function m(e,n){b.set(e,n)}f("mapped",(e=>b.get(e)));let v=!1,w=null;const y=new WeakMap;function T(e){const n=g(e);v&&n&&!n.processedForDebugging&&(e instanceof Element?function(e,n){"OPTION"===e.tagName?n.parent?.selectOpen&&w&&(w.innerHTML+="<br>"+E(n.config),n.processedForDebugging=!0):(e.style.outline="solid 1px red",e.addEventListener("mouseenter",(()=>{o(w);const e=[n];let t=n.parent;for(;t;)e.push(t),t=t.parent;C(e.map((e=>E(e.config))).join("<br>"))}),{capture:!0}),e.addEventListener("mouseleave",(()=>{o(w),w.style.display="none"}),{capture:!0}),n.processedForDebugging=!0)}(e,n):function(e,n){let t=y.get(e);t||(t=document.createElement("div"),t.classList.add("ve-debug"),t.style.background="black",t.style.color="white",t.style.zIndex="100000",t.textContent=E(n.config),y.set(e,t),setTimeout((()=>{n.size?.width&&n.size?.height||(t?.parentElement?.removeChild(t),y.delete(e))}),1e4));const r=parent instanceof HTMLElement?parent:y.get(parent)||w;o(r),r.classList.contains("ve-debug")?(t.style.marginLeft="10px",r.appendChild(t)):(t.style.position="absolute",r.insertBefore(t,r.firstChild))}(e,n))}function C(e){w&&(w.style.display="block",w.innerHTML=e)}function k(e,n,t){v&&C(`${e}: ${n?E(n):""}; ${t?"context: "+t:""}`)}function E(e){const n=[c[e.ve]];return e.context&&n.push(`context: ${e.context}`),e.parent&&n.push(`parent: ${e.parent}`),e.track?.size&&n.push(`track: ${[...e.track?.entries()].map((([e,n])=>`${e}${n?`: ${n}`:""}`)).join(", ")}`),n.join("; ")}function M(e){const n=[],t=[],o=[],r=(e,n,t)=>{for(const r of e)o.push({element:r,parent:n,slot:t})};for(const n of e)r(n.body.children);let s=0;for(;;){const e=o[s++];if(!e)break;const{element:c,slot:l}=e;let{parent:a}=e;c.assignedSlot&&c.assignedSlot!==l||(i(c)&&(n.push({element:c,parent:a}),a=c),"slot"===c.localName&&c.assignedElements().length?r(c.assignedElements(),a,c):r(c.children,a),c.shadowRoot&&(t.push(c.shadowRoot),r(c.shadowRoot.children,a)))}return{loggables:n,shadowRoots:t}}globalThis.setVeDebuggingEnabled=function(e){v=e,e&&!w&&(w=document.createElement("div"),w.classList.add("ve-debug"),w.style.position="absolute",w.style.bottom="100px",w.style.left="100px",w.style.background="black",w.style.color="white",w.style.zIndex="100000",document.body.appendChild(w))};const P=10;function x(e,n){const t=function(e,n){const t=Math.max(e.left,n.left),o=Math.min(e.left+e.width,n.left+n.width);if(t<=o){const r=Math.max(e.top,n.top),i=Math.min(e.top+e.height,n.top+n.height);if(r<=i)return new DOMRect(t,r,o-t,i-r)}return null}(n,e.getBoundingClientRect());return!t||t.width<P||t.height<P?null:t}function L(e){const t=e.map((e=>{const n=g(e);o(n);const t={id:n.veid,type:n.config.ve};return void 0!==n.context&&(t.context=n.context),n.parent&&(t.parent=n.parent.veid),n.size&&(t.width=n.size.width,t.height=n.size.height),t}));t.length&&n.InspectorFrontendHost.InspectorFrontendHostInstance.recordImpression({impressions:t})}const D=e=>(t,o)=>{const r=g(t);if(!r)return;r.size=o;const i={veid:r.veid,width:r.size.width,height:r.size.height};e.schedule((async()=>{n.InspectorFrontendHost.InspectorFrontendHostInstance.recordResize(i),k("Resize",r?.config)}))},I=e=>(t,o,r)=>{const i=g(t);if(!i)return;const s=o instanceof MouseEvent?o.button:0,c={veid:i.veid,mouseButton:s,doubleClick:Boolean(r?.doubleClick)};e.schedule((async()=>{n.InspectorFrontendHost.InspectorFrontendHostInstance.recordClick(c),k("Click",i?.config)}))},S=e=>async t=>{const r=g(t.currentTarget);o(r);const i={veid:r.veid};e.schedule((async()=>{n.InspectorFrontendHost.InspectorFrontendHostInstance.recordHover(i),k("Hover",r?.config)}))},H=e=>async t=>{const r=g(t.currentTarget);o(r);const i={veid:r.veid};e.schedule((async()=>{n.InspectorFrontendHost.InspectorFrontendHostInstance.recordDrag(i),k("Drag",r?.config)}))};async function z(e){const t=g(e.currentTarget);o(t);const r={veid:t.veid};n.InspectorFrontendHost.InspectorFrontendHostInstance.recordChange(r),k("Change",t?.config)}const F=(e,t)=>(o,r)=>{if(!(o instanceof KeyboardEvent))return;if(t?.length&&!t.includes(o.code))return;const i=g(o.currentTarget),s={veid:i?.veid};r&&(s.context=r),e.schedule((async()=>{n.InspectorFrontendHost.InspectorFrontendHostInstance.recordKeyDown(s),k("KeyDown",i?.config)}))},R=new Map;function B(e){R.delete(e)}const O=50,$={schedule:async()=>{}};let A=$,V=$,j=$,K=$,N=$,W=$;const U=new WeakMap,_=[];function q(e){for(const n of e)if(!U.has(n)){const e=new MutationObserver(Y);e.observe(n,{attributes:!0,childList:!0,subtree:!0}),U.set(n,e)}}let G=!1;async function J(n){G=!0,A=n?.processingThrottler||new e.Throttler.Throttler(500),V=n?.keyboardLogThrottler||new e.Throttler.Throttler(3e3),j=n?.hoverLogThrottler||new e.Throttler.Throttler(1e3),K=n?.dragLogThrottler||new e.Throttler.Throttler(500),N=n?.clickLogThrottler||new e.Throttler.Throttler(500),W=n?.resizeLogThrottler||new e.Throttler.Throttler(1e3),await Q(document)}async function Q(e){_.push(e),["interactive","complete"].includes(e.readyState)&&await Z(),e.addEventListener("visibilitychange",Y),e.addEventListener("scroll",Y),q([e.body])}function X(){G=!1,R.clear();for(const e of _)e.removeEventListener("visibilitychange",Y),e.removeEventListener("scroll",Y),U.get(e.body)?.disconnect(),U.delete(e.body);const{shadowRoots:e}=M(_);for(const n of e)U.get(n)?.disconnect(),U.delete(n);_.length=0,A=$}function Y(){A&&A.schedule((()=>t.RenderCoordinator.RenderCoordinator.instance().read("processForLogging",Z)))}async function Z(){if(document.hidden)return;const e=performance.now(),{loggables:t,shadowRoots:o}=M(_),r=[],i=new Map;q(o);const c=e=>{const n=e.ownerDocument,t=i.get(n)||new DOMRect(0,0,n.defaultView?.innerWidth||0,n.defaultView?.innerHeight||0);return i.set(n,t),t};for(const{element:e,parent:o}of t){const t=p(e,s(e),o);if(!t.impressionLogged){const n=x(e,c(e)),o="OPTION"===e.tagName&&t.parent?.selectOpen;(n||o)&&(n&&(t.size=n),r.push(e),t.impressionLogged=!0)}if(!t.processed){t.context=await ee(t.config.context),t.config.track?.has("click")&&e.addEventListener("click",(e=>{const n=e.currentTarget;I(N)(n,e)}),{capture:!0}),t.config.track?.has("dblclick")&&e.addEventListener("dblclick",(e=>{const n=e.currentTarget;I(N)(n,e,{doubleClick:!0})}),{capture:!0});const o=t.config.track?.has("hover");if(o){e.addEventListener("mouseover",S(j),{capture:!0});const n=()=>Promise.resolve();e.addEventListener("mouseout",(()=>j.schedule(n)),{capture:!0})}const r=t.config.track?.has("drag");if(r){e.addEventListener("pointerdown",H(K),{capture:!0});const n=()=>Promise.resolve();e.addEventListener("pointerup",(()=>K.schedule(n)),{capture:!0})}t.config.track?.has("change")&&e.addEventListener("change",z,{capture:!0});const i=t.config.track?.has("keydown"),s=t.config.track?.get("keydown")?.split("|")||[];if(i&&e.addEventListener("keydown",F(V,s),{capture:!0}),t.config.track?.has("resize")){const n=()=>{const n=x(e,c(e))||new DOMRect(0,0,0,0);t.size&&(Math.abs(n.width-t.size.width)>=O||Math.abs(n.height-t.size.height)>=O)&&D(W)(e,n)};new ResizeObserver(n).observe(e),new IntersectionObserver(n).observe(e)}if("SELECT"===e.tagName){const o=()=>{t.selectOpen||(t.selectOpen=!0,Y())};e.addEventListener("click",o,{capture:!0}),e.addEventListener("keydown",(e=>{const t=e;(!n.Platform.isMac()&&!t.altKey||"ArrowDown"!==t.code&&"ArrowUp"!==t.code)&&(t.altKey||t.ctrlKey||"F4"!==t.code)||o()}),{capture:!0}),e.addEventListener("keypress",(e=>{const t=e;(" "===t.key||!n.Platform.isMac()&&"\r"===t.key)&&o()}),{capture:!0}),e.addEventListener("change",(n=>{for(const t of e.selectedOptions)g(t)?.config.track?.has("click")&&I(N)(t,n)}),{capture:!0})}t.processed=!0}T(e)}for(const{loggable:e,config:n,parent:t}of[...R.values()]){const o=p(e,n,t);(!o.parent||o.parent.impressionLogged)&&(o.context=await ee(o.config.context),T(e),r.push(e),o.impressionLogged=!0,B(e))}await L(r),n.userMetrics.visualLoggingProcessingDone(performance.now()-e)}async function ee(e){if(void 0===e)return;const n=parseInt(e,10);if(!isNaN(n))return n;if(!crypto.subtle)return 3735928559;const t=(new TextEncoder).encode(e),o=await crypto.subtle.digest("SHA-1",t);return new DataView(o).getUint32(0,!0)}const ne=(e,n)=>I(N)(e,n),te=(e,n)=>D(W)(e,n),oe=async(e,n)=>F(V)(e,await ee(n));function re(e,n,t){G&&(!function(e,n,t){R.set(e,{loggable:e,config:n,parent:t})}(e,l(n),t||void 0),Y())}const ie=a.bind(null,"Action"),se=a.bind(null,"Adorner"),ce=a.bind(null,"AnimationClip"),le=a.bind(null,"BezierCurveEditor"),ae=a.bind(null,"BezierPresetCategory"),de=a.bind(null,"BreakpointMarker"),ue=a.bind(null,"Canvas"),pe=a.bind(null,"Close"),ge=a.bind(null,"ColorEyeDropper"),he=a.bind(null,"Counter"),fe=a.bind(null,"ControlPoint"),be=a.bind(null,"CssColorMix"),me=a.bind(null,"DeviceModeRuler"),ve=a.bind(null,"DOMBreakpoint"),we=a.bind(null,"Drawer"),ye=a.bind(null,"DropDown"),Te=a.bind(null,"ElementsBreadcrumbs"),Ce=a.bind(null,"Expand"),ke=a.bind(null,"FilterDropdown"),Ee=a.bind(null,"Dialog"),Me=a.bind(null,"Item"),Pe=a.bind(null,"Key"),xe=a.bind(null,"Link"),Le=a.bind(null,"MediaInspectorView"),De=a.bind(null,"Menu"),Ie=a.bind(null,"MetricsBox"),Se=a.bind(null,"PaletteColorShades"),He=a.bind(null,"Pane"),ze=a.bind(null,"Panel"),Fe=a.bind(null,"PanelTabHeader"),Re=a.bind(null,"PieChart"),Be=a.bind(null,"PieChartSlice"),Oe=a.bind(null,"PieChartTotal"),$e=a.bind(null,"Popover"),Ae=a.bind(null,"Preview"),Ve=a.bind(null,"Resizer"),je=a.bind(null,"ResponsivePresets"),Ke=a.bind(null,"ShowStyleEditor"),Ne=a.bind(null,"Slider"),We=a.bind(null,"Section"),Ue=a.bind(null,"SectionHeader"),_e=a.bind(null,"StylesSelector"),qe=a.bind(null,"TableCell"),Ge=a.bind(null,"TableHeader"),Je=a.bind(null,"TextField"),Qe=a.bind(null,"Timeline"),Xe=a.bind(null,"Toggle"),Ye=a.bind(null,"Toolbar"),Ze=a.bind(null,"ToggleSubpane"),en=a.bind(null,"Tree"),nn=a.bind(null,"TreeItem"),tn=a.bind(null,"Value");export{ie as action,Q as addDocument,se as adorner,ce as animationClip,le as bezierCurveEditor,ae as bezierPresetCategory,de as breakpointMarker,ue as canvas,pe as close,ge as colorEyeDropper,fe as controlPoint,he as counter,be as cssColorMix,me as deviceModeRuler,Ee as dialog,ve as domBreakpoint,we as drawer,ye as dropDown,Te as elementsBreadcrumbs,Ce as expand,ke as filterDropdown,Me as item,Pe as key,xe as link,z as logChange,ne as logClick,L as logImpressions,oe as logKeyDown,te as logResize,Le as mediaInspectorView,De as menu,Ie as metricsBox,Se as paletteColorShades,He as pane,ze as panel,Fe as panelTabHeader,Re as pieChart,Be as pieChartSlice,Oe as pieChartTotal,$e as popover,Ae as preview,re as registerLoggable,f as registerParentProvider,Ve as resizer,je as responsivePresets,We as section,Ue as sectionHeader,m as setMappedParent,Ke as showStyleEditor,Ne as slider,J as startLogging,X as stopLogging,_e as stylesSelector,qe as tableCell,Ge as tableHeader,Je as textField,Qe as timeline,Xe as toggle,Ze as toggleSubpane,Ye as toolbar,en as tree,nn as treeItem,tn as value};
