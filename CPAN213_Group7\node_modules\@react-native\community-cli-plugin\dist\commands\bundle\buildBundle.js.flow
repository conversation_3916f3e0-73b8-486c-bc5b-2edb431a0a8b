/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 * @oncall react_native
 */

import type { Config } from "@react-native-community/cli-types";
import type { ConfigT } from "metro-config";
import metroBundle from "metro/src/shared/output/bundle";
import metroRamBundle from "metro/src/shared/output/RamBundle";
export type BundleCommandArgs = {
  assetsDest?: string,
  assetCatalogDest?: string,
  entryFile: string,
  resetCache: boolean,
  resetGlobalCache: boolean,
  transformer?: string,
  minify?: boolean,
  config?: string,
  platform: string,
  dev: boolean,
  bundleOutput: string,
  bundleEncoding?: "utf8" | "utf16le" | "ascii",
  maxWorkers?: number,
  sourcemapOutput?: string,
  sourcemapSourcesRoot?: string,
  sourcemapUseAbsolutePath: boolean,
  verbose: boolean,
  unstableTransformProfile: string,
  indexedRamBundle?: boolean,
  resolverOption?: Array<string>,
};

declare function buildBundle(
  _argv: Array<string>,
  ctx: Config,
  args: BundleCommandArgs,
  bundleImpl: typeof metroBundle | typeof metroRamBundle
): Promise<void>;

declare function buildBundleWithConfig(
  args: BundleCommandArgs,
  config: ConfigT,
  bundleImpl: typeof metroBundle | typeof metroRamBundle
): Promise<void>;

/**
 * UNSTABLE: This function is likely to be relocated and its API changed in
 * the near future. `@react-native/community-cli-plugin` should not be directly
 * depended on by projects or integrators -- this is exported for legacy
 * compatibility.
 *
 * Create a bundle using a pre-loaded Metro config. The config can be
 * re-used for several bundling calls if multiple platforms are being
 * bundled.
 */
declare export const unstable_buildBundleWithConfig: typeof buildBundleWithConfig;

declare export default typeof buildBundle;
