import*as e from"../code_highlighter/code_highlighter.js";import*as t from"../../lit-html/lit-html.js";import*as o from"../../visual_logging/visual_logging.js";const s=new CSSStyleSheet;s.replaceSync(".token-variable{color:var(--sys-color-token-variable)}.token-property{color:var(--sys-color-token-property)}.token-type{color:var(--sys-color-token-type)}.token-variable-special{color:var(--sys-color-token-variable-special)}.token-definition{color:var(--sys-color-token-definition)}.token-builtin{color:var(--sys-color-token-builtin)}.token-number{color:var(--sys-color-token-number)}.token-string{color:var(--sys-color-token-string)}.token-string-special{color:var(--sys-color-token-string-special)}.token-atom{color:var(--sys-color-token-atom)}.token-keyword{color:var(--sys-color-token-keyword)}.token-comment{color:var(--sys-color-token-comment)}.token-meta{color:var(--sys-color-token-meta)}.token-invalid{color:var(--sys-color-error)}.token-tag{color:var(--sys-color-token-tag)}.token-attribute{color:var(--sys-color-token-attribute)}.token-attribute-value{color:var(--sys-color-token-attribute-value)}.token-inserted{color:var(--sys-color-token-inserted)}.token-deleted{color:var(--sys-color-token-deleted)}.token-heading{color:var(--sys-color-token-variable-special);font-weight:bold}.token-link{color:var(--sys-color-token-variable-special);text-decoration:underline}.token-strikethrough{text-decoration:strike-through}.token-strong{font-weight:bold}.token-emphasis{font-style:italic}\n/*# sourceURL=codeHighlighter.css */\n");const i=new CSSStyleSheet;i.replaceSync('*{box-sizing:border-box;font-size:inherit;margin:0;padding:0}:host{position:relative}devtools-editable-content{background:transparent;border:none;color:var(--override-color-recorder-input,var(--sys-color-on-surface));cursor:text;display:inline-block;line-height:18px;min-height:18px;min-width:0.5em;outline:none;overflow-wrap:anywhere}devtools-editable-content:hover,\ndevtools-editable-content:focus{box-shadow:0 0 0 1px var(--sys-color-divider);border-radius:2px}devtools-editable-content[placeholder]:empty::before{content:attr(placeholder);color:var(--sys-color-on-surface);opacity:50%}devtools-editable-content[placeholder]:empty:focus::before{content:""}devtools-suggestion-box{position:absolute;display:none}devtools-editable-content:focus ~ devtools-suggestion-box{display:block}.suggestions{background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);min-height:1em;min-width:150px;overflow-x:hidden;overflow-y:auto;position:relative;z-index:100;max-height:350px}.suggestions > li{padding:1px;border:1px solid transparent;white-space:nowrap;font-family:var(--source-code-font-family);font-size:var(--source-code-font-size);color:var(--sys-color-on-surface)}.suggestions > li:hover{background-color:var(--sys-color-state-hover-on-subtle)}.suggestions > li.selected{background-color:var(--sys-color-primary);color:var(--sys-color-cdt-base-container)}.strikethrough{text-decoration:line-through}\n/*# sourceURL=suggestionInput.css */\n');var n=self&&self.__decorate||function(e,t,o,s){var i,n=arguments.length,r=n<3?t:null===s?s=Object.getOwnPropertyDescriptor(t,o):s;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)r=Reflect.decorate(e,t,o,s);else for(var l=e.length-1;l>=0;l--)(i=e[l])&&(r=(n<3?i(r):n>3?i(t,o,r):i(t,o))||r);return n>3&&r&&Object.defineProperty(t,o,r),r};function r(e,t="Assertion failed!"){if(!e)throw new Error(t)}const{html:l,Decorators:a,Directives:c,LitElement:h}=t,{customElement:d,property:u,state:g}=a,{classMap:p}=c,v={hasChanged:(e,t)=>JSON.stringify(e)!==JSON.stringify(t)};let y=class extends HTMLElement{static get observedAttributes(){return["disabled","placeholder"]}set disabled(e){this.contentEditable=String(!e)}get disabled(){return"true"!==this.contentEditable}set value(e){this.innerText=e,this.#e()}get value(){return this.innerText}set mimeType(e){this.#t=e,this.#e()}get mimeType(){return this.#t}#t="";constructor(){super(),this.contentEditable="true",this.tabIndex=0,this.addEventListener("focus",(()=>{this.innerHTML=this.innerText})),this.addEventListener("blur",this.#e.bind(this))}#e(){this.#t&&e.CodeHighlighter.highlightNode(this,this.#t)}attributeChangedCallback(e,t,o){if("disabled"===e)this.disabled=null!==o}};y=n([d("devtools-editable-content")],y);class b extends Event{static eventName="suggest";constructor(e){super(b.eventName),this.suggestion=e}}class m extends Event{static eventName="suggestioninit";listeners;constructor(e){super(m.eventName),this.listeners=e}}const k=(e,t)=>e.toLowerCase().startsWith(t.toLowerCase());let f=class extends h{static styles=[i];#o=[];constructor(){super(),this.options=[],this.expression="",this.cursor=0}#s=e=>{if(r(e instanceof KeyboardEvent,"Bound to the wrong event."),this.#o.length>0)switch(e.key){case"ArrowDown":e.stopPropagation(),e.preventDefault(),this.#i(1);break;case"ArrowUp":e.stopPropagation(),e.preventDefault(),this.#i(-1)}if("Enter"===e.key)this.#o[this.cursor]&&this.#n(this.#o[this.cursor]),e.preventDefault()};#i(e){var t,o;this.cursor=(t=this.cursor+e,o=this.#o.length,(t%o+o)%o)}#n(e){this.dispatchEvent(new b(e))}connectedCallback(){super.connectedCallback(),this.dispatchEvent(new m([["keydown",this.#s]]))}willUpdate(e){e.has("options")&&(this.options=Object.freeze([...this.options].sort())),(e.has("expression")||e.has("options"))&&(this.cursor=0,this.#o=this.options.filter((e=>(this.suggestionFilter||k)(e,this.expression))))}render(){if(0!==this.#o.length)return l`<ul class="suggestions">
      ${this.#o.map(((e,t)=>l`<li
          class=${p({selected:t===this.cursor})}
          @mousedown=${this.#n.bind(this,e)}
        >
          ${e}
        </li>`))}
    </ul>`}};n([u(v)],f.prototype,"options",void 0),n([u()],f.prototype,"expression",void 0),n([u()],f.prototype,"suggestionFilter",void 0),n([g()],f.prototype,"cursor",void 0),f=n([d("devtools-suggestion-box")],f);let x=class extends h{static shadowRootOptions={...h.shadowRootOptions,delegatesFocus:!0};static styles=[i,s];constructor(){super(),this.options=[],this.expression="",this.placeholder="",this.value="",this.disabled=!1,this.strikethrough=!0,this.mimeType="",this.autocomplete=!0,this.addEventListener("blur",this.#r);let e=o.value().track({keydown:!0});this.jslogContext&&(e=e.context(this.jslogContext)),this.setAttribute("jslog",e.toString())}#l;get#a(){if(this.#l)return this.#l;const e=this.renderRoot.querySelector("devtools-editable-content");if(!e)throw new Error("Attempted to query node before rendering.");return this.#l=e,e}#r=()=>{window.getSelection()?.removeAllRanges(),this.value=this.#a.value,this.expression=this.#a.value};#c=e=>{r(e.target instanceof Node);const t=document.createRange();t.selectNodeContents(e.target);const o=window.getSelection();o.removeAllRanges(),o.addRange(t)};#s=e=>{"Enter"===e.key&&e.preventDefault()};#h=e=>{this.expression=e.target.value};#d=e=>{for(const[t,o]of e.listeners)this.addEventListener(t,o)};#u=e=>{this.#a.value=e.suggestion,setTimeout(this.blur.bind(this),0)};willUpdate(e){e.has("value")&&(this.expression=this.value)}render(){return l`<devtools-editable-content
        ?disabled=${this.disabled}
        class=${p({strikethrough:!this.strikethrough})}
        .enterKeyHint=${"done"}
        .value=${this.value}
        .mimeType=${this.mimeType}
        @focus=${this.#c}
        @input=${this.#h}
        @keydown=${this.#s}
        autocapitalize="off"
        inputmode="text"
        placeholder=${this.placeholder}
        spellcheck="false"
      ></devtools-editable-content>
      <devtools-suggestion-box
        @suggestioninit=${this.#d}
        @suggest=${this.#u}
        .options=${this.options}
        .suggestionFilter=${this.suggestionFilter}
        .expression=${this.autocomplete?this.expression:""}
      ></devtools-suggestion-box>`}};n([u(v)],x.prototype,"options",void 0),n([u()],x.prototype,"autocomplete",void 0),n([u()],x.prototype,"suggestionFilter",void 0),n([g()],x.prototype,"expression",void 0),n([u()],x.prototype,"placeholder",void 0),n([u()],x.prototype,"value",void 0),n([u({type:Boolean})],x.prototype,"disabled",void 0),n([u()],x.prototype,"strikethrough",void 0),n([u()],x.prototype,"mimeType",void 0),n([u()],x.prototype,"jslogContext",void 0),x=n([d("devtools-suggestion-input")],x);var w=Object.freeze({__proto__:null,get SuggestionInput(){return x}});export{w as SuggestionInput};
